# External Secrets Configuration for Local Environment
# Note: For local development, you typically use local secrets instead of external ones
# This file is provided for completeness and testing external secrets locally

apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: merlinhelm-secret-store
  namespace: merlinhelm-local
spec:
  provider:
    # For local development, you might use a local vault or file-based provider
    # This example shows GitHub provider for consistency
    github:
      auth:
        token:
          secretRef:
            name: github-token
            key: token
      owner: "your-github-org"  # Replace with your GitHub organization
      repo: "MerlinHelm"        # Replace with your repository name

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: merlinhelm-openwebui-secrets
  namespace: merlinhelm-local
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: merlinhelm-secret-store
    kind: SecretStore
  target:
    name: openwebui-external-secrets
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        secret-key: "{{ .secretKey }}"
        jwt-secret: "{{ .jwtSecret }}"
        database-password: "{{ .databasePassword }}"
        oauth-client-secret: "{{ .oauthClientSecret }}"
        smtp-password: "{{ .smtpPassword }}"
        openai-api-key: "{{ .openaiApiKey }}"
  data:
  - secretKey: "secretKey"
    remoteRef:
      key: "MERLINHELM_LOCAL_OPENWEBUI_SECRET_KEY"
  - secretKey: "jwtSecret"
    remoteRef:
      key: "MERLINHELM_LOCAL_OPENWEBUI_JWT_SECRET"
  - secretKey: "databasePassword"
    remoteRef:
      key: "MERLINHELM_LOCAL_DATABASE_PASSWORD"
  - secretKey: "oauthClientSecret"
    remoteRef:
      key: "MERLINHELM_LOCAL_OAUTH_CLIENT_SECRET"
  - secretKey: "smtpPassword"
    remoteRef:
      key: "MERLINHELM_LOCAL_SMTP_PASSWORD"
  - secretKey: "openaiApiKey"
    remoteRef:
      key: "MERLINHELM_LOCAL_OPENAI_API_KEY"

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: merlinhelm-ollama-secrets
  namespace: merlinhelm-local
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: merlinhelm-secret-store
    kind: SecretStore
  target:
    name: ollama-external-secrets
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        huggingface-token: "{{ .huggingfaceToken }}"
        registry-password: "{{ .registryPassword }}"
  data:
  - secretKey: "huggingfaceToken"
    remoteRef:
      key: "MERLINHELM_LOCAL_HUGGINGFACE_TOKEN"
  - secretKey: "registryPassword"
    remoteRef:
      key: "MERLINHELM_LOCAL_REGISTRY_PASSWORD"
