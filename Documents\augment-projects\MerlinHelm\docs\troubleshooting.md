# MerlinHelm Troubleshooting Guide

This guide helps you diagnose and resolve common issues with MerlinHelm deployments.

## 🔍 General Debugging Steps

### 1. Check Overall Status
```powershell
# Check all resources in namespace
kubectl get all -n merlinhelm-dev

# Check events
kubectl get events -n merlinhelm-dev --sort-by='.lastTimestamp'

# Check resource usage
kubectl top pods -n merlinhelm-dev
kubectl top nodes
```

### 2. Examine Pod Status
```powershell
# Detailed pod information
kubectl describe pod <pod-name> -n merlinhelm-dev

# Pod logs
kubectl logs <pod-name> -n merlinhelm-dev

# Previous container logs (if pod restarted)
kubectl logs <pod-name> -n merlinhelm-dev --previous
```

## 🚨 Common Issues and Solutions

### Issue 1: Pods Stuck in Pending State

#### Symptoms
- Pods show `Pending` status
- No containers are running

#### Diagnosis
```powershell
kubectl describe pod <pod-name> -n merlinhelm-dev
```

#### Common Causes and Solutions

**Insufficient Resources**
```
Error: 0/3 nodes are available: 3 Insufficient memory.
```
**Solution**: Reduce resource requests or add more nodes
```yaml
resources:
  requests:
    memory: 2Gi  # Reduce from 4Gi
    cpu: 500m    # Reduce from 1000m
```

**GPU Not Available**
```
Error: 0/3 nodes are available: 3 Insufficient nvidia.com/gpu.
```
**Solution**: 
1. Check GPU nodes: `kubectl get nodes -l accelerator=nvidia-tesla-k80`
2. Verify GPU device plugin: `kubectl get pods -n kube-system | grep nvidia`
3. Label GPU nodes: `kubectl label nodes <node> accelerator=nvidia-tesla-k80`

**Node Selector Mismatch**
```
Error: 0/3 nodes are available: 3 node(s) didn't match Pod's node affinity/selector.
```
**Solution**: Update node selector or label nodes correctly
```yaml
nodeSelector:
  kubernetes.io/os: linux  # More generic selector
```

### Issue 2: Ollama Pod Crashes or Restarts

#### Symptoms
- Ollama pod shows `CrashLoopBackOff`
- High restart count

#### Diagnosis
```powershell
kubectl logs ollama-<pod-id> -n merlinhelm-dev
kubectl describe pod ollama-<pod-id> -n merlinhelm-dev
```

#### Common Causes and Solutions

**GPU Memory Issues**
```
Error: CUDA out of memory
```
**Solution**: Reduce model size or increase GPU memory
```yaml
models:
  preload:
    - llama2:7b  # Use smaller model instead of 13b/70b
```

**Storage Issues**
```
Error: No space left on device
```
**Solution**: Increase persistent volume size
```yaml
persistence:
  size: 100Gi  # Increase from 50Gi
```

**Permission Issues**
```
Error: Permission denied
```
**Solution**: Check security context and volume permissions
```yaml
securityContext:
  runAsUser: 0  # Run as root if needed
  runAsNonRoot: false
```

### Issue 3: OpenWebUI Cannot Connect to Ollama

#### Symptoms
- OpenWebUI loads but shows "Connection Error"
- Cannot list models

#### Diagnosis
```powershell
# Check if Ollama service is accessible
kubectl port-forward svc/ollama 11434:11434 -n merlinhelm-dev
curl http://localhost:11434/api/tags

# Check OpenWebUI logs
kubectl logs deployment/openwebui -n merlinhelm-dev
```

#### Solutions

**Service Name Resolution**
```yaml
env:
  - name: OLLAMA_BASE_URL
    value: "http://ollama.merlinhelm-dev.svc.cluster.local:11434"
```

**Network Policy Issues**
```powershell
# Check if network policies are blocking traffic
kubectl get networkpolicies -n merlinhelm-dev

# Temporarily disable network policies for testing
kubectl delete networkpolicy --all -n merlinhelm-dev
```

### Issue 4: Ingress Not Working

#### Symptoms
- Cannot access OpenWebUI via domain
- 404 or connection timeout errors

#### Diagnosis
```powershell
# Check ingress status
kubectl get ingress -n merlinhelm-dev
kubectl describe ingress openwebui -n merlinhelm-dev

# Check ingress controller
kubectl get pods -n ingress-nginx
kubectl logs -n ingress-nginx deployment/ingress-nginx-controller
```

#### Solutions

**Ingress Controller Not Ready**
```powershell
# Restart ingress controller
kubectl rollout restart deployment/ingress-nginx-controller -n ingress-nginx

# Check service type
kubectl get svc -n ingress-nginx
```

**DNS Issues**
```powershell
# Add to hosts file for local testing
# Windows: C:\Windows\System32\drivers\etc\hosts
# Linux/Mac: /etc/hosts
127.0.0.1 openwebui.dev.merlinhelm.local
```

**TLS Certificate Issues**
```powershell
# Check certificate status
kubectl get certificates -n merlinhelm-dev
kubectl describe certificate openwebui-tls -n merlinhelm-dev

# Check cert-manager logs
kubectl logs -n cert-manager deployment/cert-manager
```

### Issue 5: High Resource Usage

#### Symptoms
- Nodes running out of memory/CPU
- Slow response times

#### Diagnosis
```powershell
# Check resource usage
kubectl top nodes
kubectl top pods -n merlinhelm-dev

# Check resource limits
kubectl describe pod <pod-name> -n merlinhelm-dev | Select-String -A 10 "Limits:"
```

#### Solutions

**Implement Resource Limits**
```yaml
resources:
  limits:
    memory: 8Gi
    cpu: 2000m
    nvidia.com/gpu: 1
  requests:
    memory: 4Gi
    cpu: 1000m
```

**Enable Auto-scaling**
```yaml
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 70
```

### Issue 6: Persistent Volume Issues

#### Symptoms
- Pods cannot start due to volume mount failures
- Data loss between restarts

#### Diagnosis
```powershell
# Check PVC status
kubectl get pvc -n merlinhelm-dev
kubectl describe pvc <pvc-name> -n merlinhelm-dev

# Check storage class
kubectl get storageclass
```

#### Solutions

**Storage Class Not Available**
```yaml
persistence:
  storageClass: "default"  # Use available storage class
```

**Volume Mount Permissions**
```yaml
securityContext:
  fsGroup: 2000  # Set group for volume access
```

## 🛠 Advanced Debugging

### Enable Debug Logging

**Ollama Debug Mode**
```yaml
env:
  - name: OLLAMA_DEBUG
    value: "true"
```

**OpenWebUI Debug Mode**
```yaml
env:
  - name: WEBUI_DEBUG
    value: "true"
  - name: LOG_LEVEL
    value: "DEBUG"
```

### Network Debugging

**Test Internal Connectivity**
```powershell
# Create debug pod
kubectl run debug --image=busybox -it --rm --restart=Never -n merlinhelm-dev -- sh

# Inside debug pod:
nslookup ollama
wget -qO- http://ollama:11434/api/tags
```

**Check Service Endpoints**
```powershell
kubectl get endpoints -n merlinhelm-dev
kubectl describe service ollama -n merlinhelm-dev
```

### Performance Debugging

**Check GPU Utilization**
```powershell
# On GPU node:
nvidia-smi

# In Ollama pod:
kubectl exec -it <ollama-pod> -n merlinhelm-dev -- nvidia-smi
```

**Monitor Resource Usage**
```powershell
# Continuous monitoring
kubectl top pods -n merlinhelm-dev --watch

# Detailed resource usage
kubectl describe node <node-name>
```

## 📊 Monitoring and Alerting

### Set Up Basic Monitoring

**Resource Monitoring**
```powershell
# Install metrics-server if not available
kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
```

**Log Aggregation**
```powershell
# View aggregated logs
kubectl logs -l app.kubernetes.io/name=ollama -n merlinhelm-dev --tail=100
kubectl logs -l app.kubernetes.io/name=openwebui -n merlinhelm-dev --tail=100
```

## 🔧 Recovery Procedures

### Restart Deployments
```powershell
# Restart Ollama
kubectl rollout restart deployment/ollama -n merlinhelm-dev

# Restart OpenWebUI
kubectl rollout restart deployment/openwebui -n merlinhelm-dev
```

### Reset to Clean State
```powershell
# Delete and recreate deployment
.\scripts\deploy.ps1 dev -Force

# Or manually:
helm uninstall merlinhelm-dev -n merlinhelm-dev
helm install merlinhelm-dev ./helm/merlin-stack --values environments/dev/values.yaml -n merlinhelm-dev
```

### Data Recovery
```powershell
# Check PVC data
kubectl exec -it <ollama-pod> -n merlinhelm-dev -- ls -la /root/.ollama

# Backup important data
kubectl cp <ollama-pod>:/root/.ollama ./backup/ -n merlinhelm-dev
```

## 📞 Getting Help

### Collect Debug Information
```powershell
# Create debug bundle
kubectl get all -n merlinhelm-dev > debug-info.txt
kubectl describe pods -n merlinhelm-dev >> debug-info.txt
kubectl logs deployment/ollama -n merlinhelm-dev >> debug-info.txt
kubectl logs deployment/openwebui -n merlinhelm-dev >> debug-info.txt
kubectl get events -n merlinhelm-dev >> debug-info.txt
```

### Community Resources
- GitHub Issues: Report bugs and get help
- Documentation: Check docs/ directory
- Kubernetes Community: General Kubernetes help
- NVIDIA Developer Forums: GPU-specific issues

## 🔄 Preventive Measures

### Regular Maintenance
- Monitor resource usage regularly
- Update Helm charts and container images
- Test deployments in staging before production
- Implement proper backup strategies
- Set up monitoring and alerting

### Best Practices
- Use resource limits and requests
- Implement health checks
- Use multiple replicas for high availability
- Regular security updates
- Proper logging and monitoring
