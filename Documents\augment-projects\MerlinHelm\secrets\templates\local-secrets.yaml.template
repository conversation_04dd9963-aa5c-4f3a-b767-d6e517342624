# Local Secrets Template
# Copy this file to secrets/local/{environment}/secrets.yaml and fill in the values
# This file is git-ignored for security

# OpenWebUI Secrets
openwebui:
  # Secret key for session management (auto-generated if empty)
  secretKey: ""
  
  # JWT secret for authentication (auto-generated if empty)
  jwtSecret: ""
  
  # Database credentials (if using external database)
  database:
    username: ""
    password: ""
    host: ""
    port: ""
    name: ""
  
  # OAuth/OIDC configuration
  oauth:
    clientId: ""
    clientSecret: ""
    issuerUrl: ""
  
  # SMTP configuration for email notifications
  smtp:
    host: ""
    port: 587
    username: ""
    password: ""
    fromEmail: ""
  
  # API keys for external services
  apiKeys:
    openai: ""
    anthropic: ""
    google: ""
    azure: ""

# Ollama Secrets
ollama:
  # API keys for model downloads
  apiKeys:
    huggingface: ""
    modelRegistry: ""
  
  # Registry credentials for private model repositories
  registry:
    username: ""
    password: ""
    server: ""

# Monitoring and Observability
monitoring:
  # Prometheus configuration
  prometheus:
    username: ""
    password: ""
  
  # Grafana configuration
  grafana:
    adminPassword: ""
    secretKey: ""
  
  # External monitoring services
  external:
    datadogApiKey: ""
    newRelicLicenseKey: ""
    splunkToken: ""

# TLS/SSL Certificates
tls:
  # Certificate files (base64 encoded)
  cert: ""
  key: ""
  ca: ""
  
  # Certificate passwords
  keyPassword: ""
  keystorePassword: ""

# Backup and Storage
backup:
  # S3-compatible storage credentials
  s3:
    accessKey: ""
    secretKey: ""
    bucket: ""
    region: ""
    endpoint: ""
  
  # Azure Blob Storage credentials
  azure:
    accountName: ""
    accountKey: ""
    containerName: ""
  
  # Google Cloud Storage credentials
  gcs:
    serviceAccountKey: ""
    bucket: ""

# Custom application secrets
custom:
  # Add your custom secrets here
  # example:
  #   apiKey: ""
  #   token: ""
