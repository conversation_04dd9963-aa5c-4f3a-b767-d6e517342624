#!/bin/bash
# Generate secrets for MerlinHelm deployment
#
# Usage: ./generate-secrets.sh <environment> [secret-type] [--force] [--format=yaml|json|env]
#
# Examples:
#   ./generate-secrets.sh dev
#   ./generate-secrets.sh prod openwebui --force
#   ./generate-secrets.sh local --format=json

set -euo pipefail

# Default values
ENVIRONMENT=""
SECRET_TYPE=""
FORCE=false
OUTPUT_FORMAT="yaml"

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SECRETS_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${CYAN}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 <environment> [secret-type] [options]

Arguments:
  environment    Environment to generate secrets for (dev, test, prod, local)
  secret-type    Specific secret type to generate (optional)
                 Options: openwebui, ollama, monitoring, tls, backup, custom

Options:
  --force        Overwrite existing secrets
  --format=TYPE  Output format: yaml, json, env (default: yaml)
  --help         Show this help message

Examples:
  $0 dev
  $0 prod openwebui --force
  $0 local --format=json
EOF
}

# Function to generate random string
generate_random_string() {
    local length=${1:-32}
    local chars=${2:-"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"}
    
    if command -v openssl >/dev/null 2>&1; then
        openssl rand -base64 $((length * 3 / 4)) | tr -d "=+/" | cut -c1-${length}
    else
        < /dev/urandom tr -dc "$chars" | head -c${length}
    fi
}

# Function to generate password
generate_password() {
    local length=${1:-16}
    local chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*"
    generate_random_string "$length" "$chars"
}

# Function to generate API key
generate_api_key() {
    local length=${1:-64}
    local chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    generate_random_string "$length" "$chars"
}

# Function to generate JWT secret
generate_jwt_secret() {
    local length=${1:-64}
    local chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
    generate_random_string "$length" "$chars"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force)
            FORCE=true
            shift
            ;;
        --format=*)
            OUTPUT_FORMAT="${1#*=}"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        -*)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
        *)
            if [[ -z "$ENVIRONMENT" ]]; then
                ENVIRONMENT="$1"
            elif [[ -z "$SECRET_TYPE" ]]; then
                SECRET_TYPE="$1"
            else
                print_error "Too many arguments"
                show_usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required arguments
if [[ -z "$ENVIRONMENT" ]]; then
    print_error "Environment is required"
    show_usage
    exit 1
fi

# Validate environment
case "$ENVIRONMENT" in
    dev|test|prod|local)
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT"
        print_error "Valid environments: dev, test, prod, local"
        exit 1
        ;;
esac

# Validate secret type if provided
if [[ -n "$SECRET_TYPE" ]]; then
    case "$SECRET_TYPE" in
        openwebui|ollama|monitoring|tls|backup|custom)
            ;;
        *)
            print_error "Invalid secret type: $SECRET_TYPE"
            print_error "Valid types: openwebui, ollama, monitoring, tls, backup, custom"
            exit 1
            ;;
    esac
fi

# Validate output format
case "$OUTPUT_FORMAT" in
    yaml|json|env)
        ;;
    *)
        print_error "Invalid output format: $OUTPUT_FORMAT"
        print_error "Valid formats: yaml, json, env"
        exit 1
        ;;
esac

# Create local secrets directory
LOCAL_SECRETS_DIR="$SECRETS_DIR/local/$ENVIRONMENT"
mkdir -p "$LOCAL_SECRETS_DIR"
print_info "Created directory: $LOCAL_SECRETS_DIR"

# Output file path
OUTPUT_FILE="$LOCAL_SECRETS_DIR/secrets.$OUTPUT_FORMAT"

# Check if file exists and force not specified
if [[ -f "$OUTPUT_FILE" && "$FORCE" != true ]]; then
    print_warning "Secrets file already exists: $OUTPUT_FILE"
    print_warning "Use --force to overwrite existing secrets"
    exit 1
fi

# Generate secrets
print_info "Generating secrets for environment: $ENVIRONMENT"

# Create temporary file for secrets
TEMP_FILE=$(mktemp)
trap 'rm -f "$TEMP_FILE"' EXIT

# Generate secrets based on format
case "$OUTPUT_FORMAT" in
    yaml)
        cat > "$TEMP_FILE" << EOF
# Generated secrets for $ENVIRONMENT environment
# Generated on: $(date -u +"%Y-%m-%d %H:%M:%S")
# WARNING: This file contains sensitive information. Do not commit to version control.

openwebui:
  secretKey: "$(generate_random_string 32)"
  jwtSecret: "$(generate_jwt_secret 64)"
  database:
    username: "openwebui_user"
    password: "$(generate_password 16)"
    host: ""
    port: "5432"
    name: "openwebui_$ENVIRONMENT"
  oauth:
    clientId: ""
    clientSecret: "$(generate_random_string 32)"
    issuerUrl: ""
  smtp:
    host: ""
    port: 587
    username: ""
    password: "$(generate_password 16)"
    fromEmail: ""
  apiKeys:
    openai: ""
    anthropic: ""
    google: ""
    azure: ""

ollama:
  apiKeys:
    huggingface: ""
    modelRegistry: ""
  registry:
    username: ""
    password: "$(generate_password 16)"
    server: ""

monitoring:
  prometheus:
    username: "prometheus"
    password: "$(generate_password 16)"
  grafana:
    adminPassword: "$(generate_password 16)"
    secretKey: "$(generate_random_string 32)"
  external:
    datadogApiKey: ""
    newRelicLicenseKey: ""
    splunkToken: ""

tls:
  cert: ""
  key: ""
  ca: ""
  keyPassword: "$(generate_password 16)"
  keystorePassword: "$(generate_password 16)"

backup:
  s3:
    accessKey: ""
    secretKey: "$(generate_random_string 40)"
    bucket: "merlinhelm-backup-$ENVIRONMENT"
    region: "us-west-2"
    endpoint: ""
  azure:
    accountName: ""
    accountKey: "$(generate_random_string 88)"
    containerName: "merlinhelm-backup-$ENVIRONMENT"
  gcs:
    serviceAccountKey: ""
    bucket: "merlinhelm-backup-$ENVIRONMENT"

custom:
  # Add your custom secrets here
EOF
        ;;
        
    json)
        cat > "$TEMP_FILE" << EOF
{
  "metadata": {
    "environment": "$ENVIRONMENT",
    "generated": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "warning": "This file contains sensitive information. Do not commit to version control."
  },
  "secrets": {
    "openwebui": {
      "secretKey": "$(generate_random_string 32)",
      "jwtSecret": "$(generate_jwt_secret 64)",
      "database": {
        "username": "openwebui_user",
        "password": "$(generate_password 16)",
        "host": "",
        "port": "5432",
        "name": "openwebui_$ENVIRONMENT"
      },
      "oauth": {
        "clientId": "",
        "clientSecret": "$(generate_random_string 32)",
        "issuerUrl": ""
      },
      "smtp": {
        "host": "",
        "port": 587,
        "username": "",
        "password": "$(generate_password 16)",
        "fromEmail": ""
      },
      "apiKeys": {
        "openai": "",
        "anthropic": "",
        "google": "",
        "azure": ""
      }
    },
    "ollama": {
      "apiKeys": {
        "huggingface": "",
        "modelRegistry": ""
      },
      "registry": {
        "username": "",
        "password": "$(generate_password 16)",
        "server": ""
      }
    },
    "monitoring": {
      "prometheus": {
        "username": "prometheus",
        "password": "$(generate_password 16)"
      },
      "grafana": {
        "adminPassword": "$(generate_password 16)",
        "secretKey": "$(generate_random_string 32)"
      },
      "external": {
        "datadogApiKey": "",
        "newRelicLicenseKey": "",
        "splunkToken": ""
      }
    },
    "tls": {
      "cert": "",
      "key": "",
      "ca": "",
      "keyPassword": "$(generate_password 16)",
      "keystorePassword": "$(generate_password 16)"
    },
    "backup": {
      "s3": {
        "accessKey": "",
        "secretKey": "$(generate_random_string 40)",
        "bucket": "merlinhelm-backup-$ENVIRONMENT",
        "region": "us-west-2",
        "endpoint": ""
      },
      "azure": {
        "accountName": "",
        "accountKey": "$(generate_random_string 88)",
        "containerName": "merlinhelm-backup-$ENVIRONMENT"
      },
      "gcs": {
        "serviceAccountKey": "",
        "bucket": "merlinhelm-backup-$ENVIRONMENT"
      }
    },
    "custom": {}
  }
}
EOF
        ;;
        
    env)
        cat > "$TEMP_FILE" << EOF
# Generated secrets for $ENVIRONMENT environment
# Generated on: $(date -u +"%Y-%m-%d %H:%M:%S")
# WARNING: This file contains sensitive information. Do not commit to version control.

MERLINHELM_${ENVIRONMENT^^}_OPENWEBUI_SECRET_KEY=$(generate_random_string 32)
MERLINHELM_${ENVIRONMENT^^}_OPENWEBUI_JWT_SECRET=$(generate_jwt_secret 64)
MERLINHELM_${ENVIRONMENT^^}_DATABASE_USERNAME=openwebui_user
MERLINHELM_${ENVIRONMENT^^}_DATABASE_PASSWORD=$(generate_password 16)
MERLINHELM_${ENVIRONMENT^^}_DATABASE_HOST=
MERLINHELM_${ENVIRONMENT^^}_DATABASE_PORT=5432
MERLINHELM_${ENVIRONMENT^^}_DATABASE_NAME=openwebui_$ENVIRONMENT
MERLINHELM_${ENVIRONMENT^^}_OAUTH_CLIENT_ID=
MERLINHELM_${ENVIRONMENT^^}_OAUTH_CLIENT_SECRET=$(generate_random_string 32)
MERLINHELM_${ENVIRONMENT^^}_OAUTH_ISSUER_URL=
MERLINHELM_${ENVIRONMENT^^}_SMTP_HOST=
MERLINHELM_${ENVIRONMENT^^}_SMTP_PORT=587
MERLINHELM_${ENVIRONMENT^^}_SMTP_USERNAME=
MERLINHELM_${ENVIRONMENT^^}_SMTP_PASSWORD=$(generate_password 16)
MERLINHELM_${ENVIRONMENT^^}_SMTP_FROM_EMAIL=
MERLINHELM_${ENVIRONMENT^^}_OPENAI_API_KEY=
MERLINHELM_${ENVIRONMENT^^}_ANTHROPIC_API_KEY=
MERLINHELM_${ENVIRONMENT^^}_GOOGLE_API_KEY=
MERLINHELM_${ENVIRONMENT^^}_AZURE_API_KEY=
MERLINHELM_${ENVIRONMENT^^}_HUGGINGFACE_TOKEN=
MERLINHELM_${ENVIRONMENT^^}_MODEL_REGISTRY_API_KEY=
MERLINHELM_${ENVIRONMENT^^}_REGISTRY_USERNAME=
MERLINHELM_${ENVIRONMENT^^}_REGISTRY_PASSWORD=$(generate_password 16)
MERLINHELM_${ENVIRONMENT^^}_REGISTRY_SERVER=
MERLINHELM_${ENVIRONMENT^^}_PROMETHEUS_USERNAME=prometheus
MERLINHELM_${ENVIRONMENT^^}_PROMETHEUS_PASSWORD=$(generate_password 16)
MERLINHELM_${ENVIRONMENT^^}_GRAFANA_ADMIN_PASSWORD=$(generate_password 16)
MERLINHELM_${ENVIRONMENT^^}_GRAFANA_SECRET_KEY=$(generate_random_string 32)
MERLINHELM_${ENVIRONMENT^^}_DATADOG_API_KEY=
MERLINHELM_${ENVIRONMENT^^}_NEWRELIC_LICENSE_KEY=
MERLINHELM_${ENVIRONMENT^^}_SPLUNK_TOKEN=
MERLINHELM_${ENVIRONMENT^^}_TLS_CERT=
MERLINHELM_${ENVIRONMENT^^}_TLS_KEY=
MERLINHELM_${ENVIRONMENT^^}_TLS_CA=
MERLINHELM_${ENVIRONMENT^^}_TLS_KEY_PASSWORD=$(generate_password 16)
MERLINHELM_${ENVIRONMENT^^}_TLS_KEYSTORE_PASSWORD=$(generate_password 16)
MERLINHELM_${ENVIRONMENT^^}_S3_ACCESS_KEY=
MERLINHELM_${ENVIRONMENT^^}_S3_SECRET_KEY=$(generate_random_string 40)
MERLINHELM_${ENVIRONMENT^^}_S3_BUCKET=merlinhelm-backup-$ENVIRONMENT
MERLINHELM_${ENVIRONMENT^^}_S3_REGION=us-west-2
MERLINHELM_${ENVIRONMENT^^}_S3_ENDPOINT=
MERLINHELM_${ENVIRONMENT^^}_AZURE_ACCOUNT_NAME=
MERLINHELM_${ENVIRONMENT^^}_AZURE_ACCOUNT_KEY=$(generate_random_string 88)
MERLINHELM_${ENVIRONMENT^^}_AZURE_CONTAINER_NAME=merlinhelm-backup-$ENVIRONMENT
MERLINHELM_${ENVIRONMENT^^}_GCS_SERVICE_ACCOUNT_KEY=
MERLINHELM_${ENVIRONMENT^^}_GCS_BUCKET=merlinhelm-backup-$ENVIRONMENT
EOF
        ;;
esac

# Move temp file to final location
mv "$TEMP_FILE" "$OUTPUT_FILE"

print_success "✅ Secrets generated successfully!"
print_info "📁 Output file: $OUTPUT_FILE"
print_info "🔒 Environment: $ENVIRONMENT"

if [[ -n "$SECRET_TYPE" ]]; then
    print_info "🎯 Secret type: $SECRET_TYPE"
fi

print_warning ""
print_warning "⚠️  SECURITY WARNING:"
print_warning "   - Keep this file secure and do not commit to version control"
print_warning "   - Rotate secrets regularly"
print_warning "   - Use different secrets for each environment"
