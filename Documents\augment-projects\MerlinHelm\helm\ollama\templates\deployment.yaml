apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ollama.fullname" . }}
  labels:
    {{- include "ollama.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "ollama.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "ollama.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "ollama.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- if .Values.runtimeClassName }}
      runtimeClassName: {{ .Values.runtimeClassName }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          {{- if .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          {{- end }}
          resources:
            {{- if .Values.gpu.enabled }}
            {{- toYaml .Values.resources | nindent 12 }}
            {{- else }}
            limits:
              {{- if .Values.resources.limits.cpu }}
              cpu: {{ .Values.resources.limits.cpu }}
              {{- end }}
              {{- if .Values.resources.limits.memory }}
              memory: {{ .Values.resources.limits.memory }}
              {{- end }}
            requests:
              {{- if .Values.resources.requests.cpu }}
              cpu: {{ .Values.resources.requests.cpu }}
              {{- end }}
              {{- if .Values.resources.requests.memory }}
              memory: {{ .Values.resources.requests.memory }}
              {{- end }}
            {{- end }}
          {{- if .Values.env }}
          env:
            {{- toYaml .Values.env | nindent 12 }}
          {{- end }}
          {{- if .Values.persistence.enabled }}
          volumeMounts:
            - name: ollama-data
              mountPath: {{ .Values.persistence.mountPath }}
          {{- end }}
      {{- if .Values.persistence.enabled }}
      volumes:
        - name: ollama-data
          persistentVolumeClaim:
            claimName: {{ include "ollama.fullname" . }}-data
      {{- end }}
      {{- if .Values.gpu.enabled }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- else }}
      nodeSelector:
        kubernetes.io/os: linux
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.gpu.enabled }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- end }}
