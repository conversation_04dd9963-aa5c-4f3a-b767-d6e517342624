name: CD - Deploy to Environments

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
        - local
        - dev
        - test
        - prod
      force_deploy:
        description: 'Force deployment (delete and recreate)'
        required: false
        default: false
        type: boolean

env:
  HELM_VERSION: v3.12.0
  KUBERNETES_VERSION: v1.27.0

jobs:
  deploy-local:
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'local'
    runs-on: ubuntu-latest
    environment: local

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Helm
      uses: azure/setup-helm@v3
      with:
        version: ${{ env.HELM_VERSION }}

    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: ${{ env.KUBERNETES_VERSION }}

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG_LOCAL }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        kubectl cluster-info

    - name: Deploy to local
      run: |
        export KUBECONFIG=kubeconfig
        chmod +x scripts/deploy.sh

        if [ "${{ github.event.inputs.force_deploy }}" == "true" ]; then
          ./scripts/deploy.sh local --force
        else
          ./scripts/deploy.sh local --upgrade
        fi

  deploy-dev:
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'dev')
    runs-on: ubuntu-latest
    environment: development

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Helm
      uses: azure/setup-helm@v3
      with:
        version: ${{ env.HELM_VERSION }}

    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: ${{ env.KUBERNETES_VERSION }}

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG_DEV }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        kubectl cluster-info

    - name: Deploy to development
      run: |
        export KUBECONFIG=kubeconfig
        chmod +x scripts/deploy.sh

        if [ "${{ github.event.inputs.force_deploy }}" == "true" ]; then
          ./scripts/deploy.sh dev --force
        else
          ./scripts/deploy.sh dev --upgrade
        fi

    - name: Run smoke tests
      run: |
        export KUBECONFIG=kubeconfig
        # Wait for pods to be ready
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=ollama -n merlinhelm-dev --timeout=300s
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=openwebui -n merlinhelm-dev --timeout=300s

        # Test Ollama API
        kubectl port-forward svc/ollama 11434:11434 -n merlinhelm-dev &
        sleep 10
        curl -f http://localhost:11434/api/tags || exit 1

        # Test OpenWebUI
        kubectl port-forward svc/openwebui 8080:8080 -n merlinhelm-dev &
        sleep 10
        curl -f http://localhost:8080/health || exit 1

  deploy-test:
    if: startsWith(github.ref, 'refs/tags/') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'test')
    runs-on: ubuntu-latest
    environment: testing
    needs: [deploy-dev]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Helm
      uses: azure/setup-helm@v3
      with:
        version: ${{ env.HELM_VERSION }}

    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: ${{ env.KUBERNETES_VERSION }}

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG_TEST }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        kubectl cluster-info

    - name: Deploy to test
      run: |
        export KUBECONFIG=kubeconfig
        chmod +x scripts/deploy.sh

        if [ "${{ github.event.inputs.force_deploy }}" == "true" ]; then
          ./scripts/deploy.sh test --force
        else
          ./scripts/deploy.sh test --upgrade
        fi

    - name: Run integration tests
      run: |
        export KUBECONFIG=kubeconfig
        # Wait for pods to be ready
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=ollama -n merlinhelm-test --timeout=600s
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=openwebui -n merlinhelm-test --timeout=600s

        # Run comprehensive tests
        echo "Running integration tests..."
        # Add your integration test commands here

  deploy-prod:
    if: startsWith(github.ref, 'refs/tags/') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'prod')
    runs-on: ubuntu-latest
    environment: production
    needs: [deploy-test]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Helm
      uses: azure/setup-helm@v3
      with:
        version: ${{ env.HELM_VERSION }}

    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: ${{ env.KUBERNETES_VERSION }}

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG_PROD }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        kubectl cluster-info

    - name: Deploy to production
      run: |
        export KUBECONFIG=kubeconfig
        chmod +x scripts/deploy.sh

        if [ "${{ github.event.inputs.force_deploy }}" == "true" ]; then
          ./scripts/deploy.sh prod --force
        else
          ./scripts/deploy.sh prod --upgrade
        fi

    - name: Run production health checks
      run: |
        export KUBECONFIG=kubeconfig
        # Wait for pods to be ready
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=ollama -n merlinhelm-prod --timeout=900s
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=openwebui -n merlinhelm-prod --timeout=900s

        # Run production health checks
        echo "Running production health checks..."
        # Add your production health check commands here

    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        text: |
          Production deployment ${{ job.status }}!
          Tag: ${{ github.ref }}
          Commit: ${{ github.sha }}
