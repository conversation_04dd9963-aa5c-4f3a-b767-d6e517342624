# GitHub Secrets Configuration

This document describes how to configure GitHub Secrets for MerlinHelm deployment.

## Required GitHub Secrets

### Development Environment
- `MERLINHELM_DEV_OPENWEBUI_SECRET_KEY`: OpenWebUI secret key for dev
- `MERLINHELM_DEV_OPENWEBUI_JWT_SECRET`: JWT secret for dev
- `MERLINHELM_DEV_DATABASE_PASSWORD`: Database password for dev
- `MERLINHELM_DEV_OAUTH_CLIENT_SECRET`: OAuth client secret for dev
- `MERLINHELM_DEV_SMTP_PASSWORD`: SMTP password for dev
- `MERLINHELM_DEV_OPENAI_API_KEY`: OpenAI API key for dev
- `MERLINHELM_DEV_HUGGINGFACE_TOKEN`: HuggingFace token for dev
- `MERLINHELM_DEV_REGISTRY_PASSWORD`: Registry password for dev
- `MERLIN<PERSON><PERSON>M_DEV_TLS_CERT`: TLS certificate for dev (base64 encoded)
- `MERLIN<PERSON><PERSON><PERSON>_DEV_TLS_KEY`: TLS private key for dev (base64 encoded)

### Test Environment
- `MERLINHELM_TEST_OPENWEBUI_SECRET_KEY`: OpenWebUI secret key for test
- `MERLINHELM_TEST_OPENWEBUI_JWT_SECRET`: JWT secret for test
- `MERLINHELM_TEST_DATABASE_PASSWORD`: Database password for test
- `MERLINHELM_TEST_OAUTH_CLIENT_SECRET`: OAuth client secret for test
- `MERLINHELM_TEST_SMTP_PASSWORD`: SMTP password for test
- `MERLINHELM_TEST_OPENAI_API_KEY`: OpenAI API key for test
- `MERLINHELM_TEST_HUGGINGFACE_TOKEN`: HuggingFace token for test
- `MERLINHELM_TEST_REGISTRY_PASSWORD`: Registry password for test
- `MERLINHELM_TEST_TLS_CERT`: TLS certificate for test (base64 encoded)
- `MERLINHELM_TEST_TLS_KEY`: TLS private key for test (base64 encoded)

### Production Environment
- `MERLINHELM_PROD_OPENWEBUI_SECRET_KEY`: OpenWebUI secret key for prod
- `MERLINHELM_PROD_OPENWEBUI_JWT_SECRET`: JWT secret for prod
- `MERLINHELM_PROD_DATABASE_PASSWORD`: Database password for prod
- `MERLINHELM_PROD_OAUTH_CLIENT_SECRET`: OAuth client secret for prod
- `MERLINHELM_PROD_SMTP_PASSWORD`: SMTP password for prod
- `MERLINHELM_PROD_OPENAI_API_KEY`: OpenAI API key for prod
- `MERLINHELM_PROD_HUGGINGFACE_TOKEN`: HuggingFace token for prod
- `MERLINHELM_PROD_REGISTRY_PASSWORD`: Registry password for prod
- `MERLINHELM_PROD_TLS_CERT`: TLS certificate for prod (base64 encoded)
- `MERLINHELM_PROD_TLS_KEY`: TLS private key for prod (base64 encoded)

### Infrastructure Secrets
- `KUBECONFIG_DEV`: Kubernetes config for dev cluster (base64 encoded)
- `KUBECONFIG_TEST`: Kubernetes config for test cluster (base64 encoded)
- `KUBECONFIG_PROD`: Kubernetes config for prod cluster (base64 encoded)
- `GITHUB_TOKEN`: GitHub token for External Secrets Operator
- `SLACK_WEBHOOK`: Slack webhook for notifications

## Setting GitHub Secrets

### Using GitHub CLI
```bash
# Set a secret using GitHub CLI
gh secret set MERLINHELM_PROD_OPENWEBUI_SECRET_KEY --body "your-secret-value"

# Set a secret from file
gh secret set MERLINHELM_PROD_TLS_CERT --body-file cert.pem

# Set a secret with base64 encoding
echo "your-secret" | base64 | gh secret set MERLINHELM_PROD_DATABASE_PASSWORD
```

### Using GitHub Web Interface
1. Go to your repository on GitHub
2. Click on "Settings" tab
3. Click on "Secrets and variables" → "Actions"
4. Click "New repository secret"
5. Enter the secret name and value
6. Click "Add secret"

### Using REST API
```bash
# Get the repository public key
curl -H "Authorization: token $GITHUB_TOKEN" \
  https://api.github.com/repos/OWNER/REPO/actions/secrets/public-key

# Encrypt and set a secret
echo "your-secret" | \
  gh secret set MERLINHELM_PROD_OPENWEBUI_SECRET_KEY
```

## Secret Naming Convention

Secrets follow this naming pattern:
```
MERLINHELM_{ENVIRONMENT}_{SERVICE}_{SECRET_TYPE}
```

Where:
- `ENVIRONMENT`: DEV, TEST, PROD, LOCAL
- `SERVICE`: OPENWEBUI, OLLAMA, DATABASE, etc.
- `SECRET_TYPE`: SECRET_KEY, PASSWORD, API_KEY, etc.

## Security Best Practices

1. **Use different secrets for each environment**
2. **Rotate secrets regularly**
3. **Use least privilege access**
4. **Monitor secret access in GitHub audit logs**
5. **Never log or expose secrets in CI/CD output**
6. **Use encrypted secrets for sensitive data**

## Troubleshooting

### Secret Not Found
- Verify the secret name matches exactly (case-sensitive)
- Check that the secret is set at the repository level
- Ensure the workflow has access to the secret

### Permission Denied
- Verify the GitHub token has the required permissions
- Check that the repository settings allow secret access
- Ensure the workflow is running from the correct branch

### External Secrets Operator Issues
- Verify ESO is installed in the cluster
- Check the SecretStore configuration
- Verify the GitHub token has repository access
