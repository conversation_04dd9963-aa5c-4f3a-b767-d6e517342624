# Getting Started with MerlinHelm

This guide will help you deploy Ollama + OpenWebUI on Kubernetes with GPU support in under 30 minutes.

## 🚀 Quick Start (TL;DR)

```powershell
# 1. Setup cluster infrastructure
.\scripts\setup-cluster.ps1 -All

# 2. Deploy to local environment (CPU-only)
.\scripts\deploy.ps1 local -Force

# 3. Access OpenWebUI
kubectl port-forward svc/merlinhelm-local-openwebui 8080:8080 -n merlinhelm-local
# Open http://localhost:8080
```

## 📋 Prerequisites

### Required Software
- **Kubernetes cluster** (local or cloud)
  - [Docker Desktop with Kubernetes](https://docs.docker.com/desktop/kubernetes/)
  - [Minikube](https://minikube.sigs.k8s.io/docs/start/)
  - [Kind](https://kind.sigs.k8s.io/docs/user/quick-start/)
  - Cloud providers (AKS, EKS, GKE)
- **Helm 3.x** - [Install Guide](https://helm.sh/docs/intro/install/)
- **kubectl** - [Install Guide](https://kubernetes.io/docs/tasks/tools/)
- **PowerShell** (Windows) or **Bash** (Linux/Mac)

### Hardware Requirements
- **Minimum**: 8GB RAM, 4 CPU cores
- **Recommended**: 16GB RAM, 8 CPU cores
- **GPU**: NVIDIA GPU with 8GB+ VRAM (for Ollama)

### For GPU Support
- NVIDIA drivers (470.57.02+)
- NVIDIA Container Toolkit
- Kubernetes GPU operator or device plugin

## 🛠 Step-by-Step Setup

### Step 1: Verify Kubernetes Cluster

```powershell
# Check cluster connection
kubectl cluster-info

# Check nodes
kubectl get nodes

# Check available resources
kubectl top nodes
```

### Step 2: Clone Repository

```powershell
git clone <your-repo-url>
cd MerlinHelm
```

### Step 3: Setup Cluster Infrastructure

```powershell
# Install all required components
.\scripts\setup-cluster.ps1 -All

# Or install components individually:
# .\scripts\setup-cluster.ps1 -InstallIngress
# .\scripts\setup-cluster.ps1 -InstallCertManager
# .\scripts\setup-cluster.ps1 -SetupGPU
# .\scripts\setup-cluster.ps1 -CreateNamespaces
```

### Step 4: Configure GPU Nodes (if available)

```powershell
# Label your GPU nodes
kubectl label nodes <node-name> accelerator=nvidia-tesla-k80
kubectl label nodes <node-name> node-type=gpu

# Verify GPU resources
kubectl describe nodes | Select-String "nvidia.com/gpu"
```

### Step 5: Deploy MerlinHelm

#### Local Environment (CPU-only)
```powershell
# Deploy to local environment (no GPU required)
.\scripts\deploy.ps1 local

# Check deployment status
kubectl get pods -n merlinhelm-local
kubectl get svc -n merlinhelm-local
```

#### Development Environment
```powershell
# Deploy to dev environment
.\scripts\deploy.ps1 dev

# Check deployment status
kubectl get pods -n merlinhelm-dev
kubectl get svc -n merlinhelm-dev
```

#### Test Environment
```powershell
# Deploy to test environment
.\scripts\deploy.ps1 test

# Check deployment status
kubectl get pods -n merlinhelm-test
```

#### Production Environment
```powershell
# Deploy to production environment
.\scripts\deploy.ps1 prod

# Check deployment status
kubectl get pods -n merlinhelm-prod
```

### Step 6: Access OpenWebUI

#### Local Access (Port Forward)
```powershell
# Port forward to access locally
kubectl port-forward svc/openwebui 8080:8080 -n merlinhelm-local

# Open browser to http://localhost:8080
```

#### Ingress Access (if configured)
```powershell
# Check ingress configuration
kubectl get ingress -n merlinhelm-dev

# Access via configured domain
# Example: https://openwebui.dev.merlinhelm.local
```

## 🔧 Configuration

### Environment-Specific Settings

#### Local (`environments/local/values.yaml`)
- CPU-only deployments (no GPU required)
- Single replica deployments
- Minimal resource allocation
- Perfect for local development without GPU hardware

#### Development (`environments/dev/values.yaml`)
- Single replica deployments
- Minimal resource allocation
- NodePort services for easy access
- Basic models (llama2:7b)

#### Test (`environments/test/values.yaml`)
- Multi-replica deployments
- Moderate resource allocation
- Ingress with staging TLS
- Multiple models for testing

#### Production (`environments/prod/values.yaml`)
- High availability deployments
- Auto-scaling enabled
- Production TLS certificates
- Full model suite

### Custom Configuration

Edit the values files to customize your deployment:

```yaml
# environments/local/values.yaml (CPU-only)
ollama:
  gpu:
    enabled: false
  resources:
    limits:
      memory: 4Gi
      cpu: 2000m
  models:
    preload:
      - llama2:7b

# environments/dev/values.yaml (GPU-enabled)
ollama:
  resources:
    limits:
      nvidia.com/gpu: 1
      memory: 8Gi
    requests:
      memory: 4Gi

  models:
    preload:
      - llama2:7b
      - codellama:7b

openwebui:
  ingress:
    hosts:
      - host: my-openwebui.local
```

## 🧪 Testing Your Deployment

### Basic Health Checks

```powershell
# Check pod status
kubectl get pods -n merlinhelm-local

# Check logs
kubectl logs -f deployment/ollama -n merlinhelm-local
kubectl logs -f deployment/openwebui -n merlinhelm-local

# Test Ollama API
kubectl port-forward svc/ollama 11434:11434 -n merlinhelm-local
curl http://localhost:11434/api/tags
```

### Load Testing

```powershell
# Scale up for testing
kubectl scale deployment ollama --replicas=2 -n merlinhelm-local
kubectl scale deployment openwebui --replicas=3 -n merlinhelm-local

# Monitor resource usage
kubectl top pods -n merlinhelm-local
```

## 🔍 Troubleshooting

### Common Issues

#### Pods Stuck in Pending
```powershell
# Check events
kubectl describe pod <pod-name> -n merlinhelm-local

# Check resource availability
kubectl describe nodes
```

#### GPU Not Available
```powershell
# Check GPU device plugin
kubectl get pods -n kube-system | Select-String nvidia

# Check node labels
kubectl get nodes --show-labels | Select-String gpu
```

#### Ingress Not Working
```powershell
# Check ingress controller
kubectl get pods -n ingress-nginx

# Check ingress configuration
kubectl describe ingress -n merlinhelm-local
```

### Useful Commands

```powershell
# View all resources
kubectl get all -n merlinhelm-local

# Check resource usage
kubectl top pods -n merlinhelm-local
kubectl top nodes

# View events
kubectl get events -n merlinhelm-local --sort-by='.lastTimestamp'

# Debug pod issues
kubectl describe pod <pod-name> -n merlinhelm-local
kubectl logs <pod-name> -n merlinhelm-local
```

## 🔄 Updating and Upgrading

### Upgrade Deployment
```powershell
# Upgrade existing deployment
.\scripts\deploy.ps1 local -Upgrade

# Force redeploy (delete and recreate)
.\scripts\deploy.ps1 local -Force
```

### Update Configuration
```powershell
# Edit values file
# environments/local/values.yaml

# Apply changes
.\scripts\deploy.ps1 local -Upgrade
```

## 🧹 Cleanup

### Quick Cleanup (Single Environment)

#### PowerShell (Windows)
```powershell
# Remove specific environment
.\scripts\teardown.ps1 local

# Remove with storage (deletes all data)
.\scripts\teardown.ps1 local -RemoveStorage
```

#### Bash (Linux/macOS)
```bash
# Remove specific environment
./scripts/teardown.sh local

# Remove with storage (deletes all data)
./scripts/teardown.sh local --remove-storage
```

### Complete Cleanup (All Environments)

#### PowerShell (Windows)
```powershell
# Remove all environments
.\scripts\teardown.ps1 all

# Remove everything including infrastructure
.\scripts\teardown.ps1 all -RemoveInfrastructure -RemoveStorage -RemoveNamespaces

# Dry run to see what would be removed
.\scripts\teardown.ps1 all -DryRun
```

#### Bash (Linux/macOS)
```bash
# Remove all environments
./scripts/teardown.sh all

# Remove everything including infrastructure
./scripts/teardown.sh all --remove-infrastructure --remove-storage --remove-namespaces

# Dry run to see what would be removed
./scripts/teardown.sh all --dry-run
```

### Manual Cleanup (if needed)
```powershell
# Remove all MerlinHelm deployments
helm uninstall merlinhelm-local -n merlinhelm-local
helm uninstall merlinhelm-dev -n merlinhelm-dev
helm uninstall merlinhelm-test -n merlinhelm-test
helm uninstall merlinhelm-prod -n merlinhelm-prod

# Delete namespaces
kubectl delete namespace merlinhelm-local merlinhelm-dev merlinhelm-test merlinhelm-prod
```

## 📚 Next Steps

1. **Explore Models**: Try different LLM models with Ollama
2. **Customize UI**: Configure OpenWebUI settings
3. **Monitor Performance**: Set up monitoring and alerting
4. **Scale Up**: Deploy to test and production environments
5. **Secure Access**: Configure authentication and authorization

## 🆘 Getting Help

- **Documentation**: Check the `docs/` directory
- **Issues**: Create GitHub issues for bugs
- **Discussions**: Use GitHub discussions for questions
- **Logs**: Always include logs when reporting issues

## 🎯 What's Next?

- [Architecture Overview](docs/architecture.md)
- [Deployment Guide](docs/deployment.md)
- [Configuration Reference](docs/configuration.md)
- [Troubleshooting Guide](docs/troubleshooting.md)
