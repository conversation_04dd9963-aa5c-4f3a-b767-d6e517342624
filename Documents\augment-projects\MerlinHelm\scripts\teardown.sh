#!/bin/bash

# MerlinHelm Teardown Script
# This script removes everything installed by setup-cluster.sh and deploy.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="all"
REMOVE_INFRASTRUCTURE=false
REMOVE_NAMESPACES=false
REMOVE_STORAGE=false
FORCE=false
DRY_RUN=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
MerlinHelm Teardown Script

Usage: $0 [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
    local               Remove local environment only
    dev                 Remove development environment only
    test                Remove test environment only
    prod                Remove production environment only
    all                 Remove all environments (default)

OPTIONS:
    --remove-infrastructure Remove cluster infrastructure (ingress, cert-manager, etc.)
    --remove-namespaces     Remove MerlinHelm namespaces
    --remove-storage        Remove persistent volumes and data
    --force                 Skip confirmation prompts
    --dry-run              Show what would be removed without actually removing
    -h, --help             Show this help message

EXAMPLES:
    $0 local                        Remove only local environment
    $0 all --remove-infrastructure  Remove everything including infrastructure
    $0 prod --dry-run              Show what would be removed from prod
    $0 --force                     Remove all environments without prompts

WARNING: This script will permanently delete deployments and data!

EOF
}

# Function to confirm actions
confirm_action() {
    if [ "$FORCE" = true ] || [ "$DRY_RUN" = true ]; then
        return 0
    fi
    
    echo -n "$1 (y/N): "
    read -r response
    case "$response" in
        [yY][eE][sS]|[yY]) 
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Function to remove Helm releases
remove_helm_releases() {
    local environments=("$@")
    
    print_status "Removing Helm releases..."
    
    for env in "${environments[@]}"; do
        local namespace="merlinhelm-$env"
        local release_name="merlinhelm-$env"
        
        if helm list -n "$namespace" 2>/dev/null | grep -q "$release_name"; then
            print_status "Found Helm release in namespace: $namespace"
            
            if [ "$DRY_RUN" = true ]; then
                print_warning "[DRY RUN] Would remove Helm release: $release_name"
            else
                if confirm_action "Remove Helm release '$release_name' from namespace '$namespace'?"; then
                    helm uninstall "$release_name" -n "$namespace"
                    print_success "Removed Helm release: $release_name"
                fi
            fi
        else
            print_status "No Helm releases found in namespace: $namespace"
        fi
    done
}

# Function to remove namespaces
remove_namespaces() {
    local environments=("$@")
    
    print_status "Removing namespaces..."
    
    for env in "${environments[@]}"; do
        local namespace="merlinhelm-$env"
        
        if kubectl get namespace "$namespace" &>/dev/null; then
            print_status "Found namespace: $namespace"
            
            if [ "$DRY_RUN" = true ]; then
                print_warning "[DRY RUN] Would remove namespace: $namespace"
            else
                if confirm_action "Remove namespace '$namespace' and all its resources?"; then
                    kubectl delete namespace "$namespace" --timeout=300s
                    print_success "Removed namespace: $namespace"
                fi
            fi
        else
            print_status "Namespace not found: $namespace"
        fi
    done
}

# Function to remove persistent volumes
remove_persistent_volumes() {
    local environments=("$@")
    
    if [ "$REMOVE_STORAGE" != true ]; then
        print_status "Skipping persistent volume removal (use --remove-storage to remove)"
        return
    fi
    
    print_status "Removing persistent volumes..."
    
    for env in "${environments[@]}"; do
        local namespace="merlinhelm-$env"
        
        if kubectl get pvc -n "$namespace" &>/dev/null; then
            print_status "Found persistent volume claims in namespace: $namespace"
            
            if [ "$DRY_RUN" = true ]; then
                print_warning "[DRY RUN] Would remove PVCs in namespace: $namespace"
                kubectl get pvc -n "$namespace"
            else
                if confirm_action "Remove all persistent volume claims in namespace '$namespace'? This will DELETE ALL DATA!"; then
                    kubectl delete pvc --all -n "$namespace"
                    print_success "Removed PVCs from namespace: $namespace"
                fi
            fi
        else
            print_status "No PVCs found in namespace: $namespace"
        fi
    done
}

# Function to remove infrastructure
remove_infrastructure() {
    if [ "$REMOVE_INFRASTRUCTURE" != true ]; then
        print_status "Skipping infrastructure removal (use --remove-infrastructure to remove)"
        return
    fi
    
    print_status "Removing cluster infrastructure..."
    
    # Remove cert-manager
    if kubectl get namespace cert-manager &>/dev/null; then
        print_status "Found cert-manager installation"
        
        if [ "$DRY_RUN" = true ]; then
            print_warning "[DRY RUN] Would remove cert-manager"
        else
            if confirm_action "Remove cert-manager and all certificates?"; then
                # Remove ClusterIssuers first
                kubectl delete clusterissuer --all &>/dev/null || true
                
                # Remove cert-manager
                helm uninstall cert-manager -n cert-manager &>/dev/null || true
                kubectl delete namespace cert-manager &>/dev/null || true
                print_success "Removed cert-manager"
            fi
        fi
    else
        print_status "cert-manager not found"
    fi
    
    # Remove NGINX Ingress Controller
    if kubectl get namespace ingress-nginx &>/dev/null; then
        print_status "Found NGINX Ingress Controller installation"
        
        if [ "$DRY_RUN" = true ]; then
            print_warning "[DRY RUN] Would remove NGINX Ingress Controller"
        else
            if confirm_action "Remove NGINX Ingress Controller?"; then
                helm uninstall ingress-nginx -n ingress-nginx &>/dev/null || true
                kubectl delete namespace ingress-nginx &>/dev/null || true
                print_success "Removed NGINX Ingress Controller"
            fi
        fi
    else
        print_status "NGINX Ingress Controller not found"
    fi
    
    # Remove GPU support
    if kubectl get daemonset nvidia-device-plugin-daemonset -n kube-system &>/dev/null; then
        print_status "Found NVIDIA Device Plugin"
        
        if [ "$DRY_RUN" = true ]; then
            print_warning "[DRY RUN] Would remove NVIDIA Device Plugin"
        else
            if confirm_action "Remove NVIDIA Device Plugin and GPU support?"; then
                kubectl delete -f ./kubernetes/gpu-runtime-class.yaml &>/dev/null || true
                print_success "Removed GPU support"
            fi
        fi
    else
        print_status "NVIDIA Device Plugin not found"
    fi
}

# Function to remove node labels
remove_node_labels() {
    print_status "Removing node labels..."
    
    local nodes
    nodes=$(kubectl get nodes -o name 2>/dev/null)
    
    for node in $nodes; do
        local node_name=${node#node/}
        
        if [ "$DRY_RUN" = true ]; then
            print_warning "[DRY RUN] Would remove labels from node: $node_name"
        else
            # Remove MerlinHelm-specific labels
            kubectl label nodes "$node_name" accelerator- &>/dev/null || true
            kubectl label nodes "$node_name" node-type- &>/dev/null || true
        fi
    done
    
    if [ "$DRY_RUN" != true ]; then
        print_success "Removed node labels"
    fi
}

# Function to show summary
show_summary() {
    local environments=("$@")
    
    print_status "Teardown Summary:"
    echo -e "  ${BLUE}Environments: ${environments[*]}${NC}"
    echo -e "  ${BLUE}Remove Infrastructure: $REMOVE_INFRASTRUCTURE${NC}"
    echo -e "  ${BLUE}Remove Namespaces: $REMOVE_NAMESPACES${NC}"
    echo -e "  ${BLUE}Remove Storage: $REMOVE_STORAGE${NC}"
    echo -e "  ${BLUE}Dry Run: $DRY_RUN${NC}"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        local|dev|test|prod|all)
            ENVIRONMENT="$1"
            shift
            ;;
        --remove-infrastructure)
            REMOVE_INFRASTRUCTURE=true
            shift
            ;;
        --remove-namespaces)
            REMOVE_NAMESPACES=true
            shift
            ;;
        --remove-storage)
            REMOVE_STORAGE=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Determine environments to remove
if [ "$ENVIRONMENT" = "all" ]; then
    environments_to_remove=("local" "dev" "test" "prod")
else
    environments_to_remove=("$ENVIRONMENT")
fi

print_status "Starting MerlinHelm teardown..."
show_summary "${environments_to_remove[@]}"

if [ "$FORCE" != true ] && [ "$DRY_RUN" != true ]; then
    print_warning "This will permanently remove MerlinHelm deployments and potentially data!"
    if ! confirm_action "Are you sure you want to continue?"; then
        print_status "Teardown cancelled by user"
        exit 0
    fi
fi

# Execute teardown steps
remove_helm_releases "${environments_to_remove[@]}"

if [ "$REMOVE_NAMESPACES" = true ]; then
    remove_namespaces "${environments_to_remove[@]}"
fi

remove_persistent_volumes "${environments_to_remove[@]}"

remove_infrastructure

remove_node_labels

if [ "$DRY_RUN" = true ]; then
    print_success "Dry run completed - no changes were made"
else
    print_success "MerlinHelm teardown completed!"
    print_status "Note: Some resources may take a few minutes to fully terminate"
fi
