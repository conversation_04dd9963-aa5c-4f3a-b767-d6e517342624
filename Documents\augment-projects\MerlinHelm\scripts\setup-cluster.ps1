# MerlinHelm Cluster Setup Script (PowerShell)
# This script sets up a Kubernetes cluster for MerlinHelm deployment

param(
    [switch]$InstallIngress,
    [switch]$InstallCertManager,
    [switch]$SetupGPU,
    [switch]$CreateNamespaces,
    [switch]$All,
    [switch]$Help
)

$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Show-Usage {
    @"
MerlinHelm Cluster Setup Script (PowerShell)

Usage: .\setup-cluster.ps1 [OPTIONS]

OPTIONS:
    -InstallIngress         Install NGINX Ingress Controller
    -InstallCertManager     Install cert-manager for TLS
    -SetupGPU              Setup GPU support (NVIDIA Device Plugin)
    -CreateNamespaces      Create MerlinHelm namespaces
    -All                   Perform all setup steps
    -Help                  Show this help message

EXAMPLES:
    .\setup-cluster.ps1 -All               Perform complete cluster setup
    .\setup-cluster.ps1 -InstallIngress    Install only NGINX Ingress

"@
}

function Install-IngressController {
    Write-Status "Installing NGINX Ingress Controller..."
    
    # Add Helm repository
    helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
    helm repo update
    
    # Install NGINX Ingress
    helm install ingress-nginx ingress-nginx/ingress-nginx `
        --namespace ingress-nginx `
        --create-namespace `
        --set controller.service.type=LoadBalancer
    
    Write-Success "NGINX Ingress Controller installed"
    
    # Wait for deployment
    Write-Status "Waiting for Ingress Controller to be ready..."
    kubectl wait --namespace ingress-nginx `
        --for=condition=ready pod `
        --selector=app.kubernetes.io/component=controller `
        --timeout=300s
    
    Write-Success "Ingress Controller is ready"
}

function Install-CertManager {
    Write-Status "Installing cert-manager..."
    
    # Add Helm repository
    helm repo add jetstack https://charts.jetstack.io
    helm repo update
    
    # Install cert-manager
    helm install cert-manager jetstack/cert-manager `
        --namespace cert-manager `
        --create-namespace `
        --set installCRDs=true
    
    Write-Success "cert-manager installed"
    
    # Wait for deployment
    Write-Status "Waiting for cert-manager to be ready..."
    kubectl wait --namespace cert-manager `
        --for=condition=ready pod `
        --selector=app.kubernetes.io/name=cert-manager `
        --timeout=300s
    
    Write-Success "cert-manager is ready"
    
    # Create ClusterIssuer for Let's Encrypt
    Write-Status "Creating Let's Encrypt ClusterIssuer..."
    
    @"
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
spec:
  acme:
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-staging
    solvers:
    - http01:
        ingress:
          class: nginx
"@ | kubectl apply -f -
    
    Write-Success "ClusterIssuers created"
}

function Setup-GPUSupport {
    Write-Status "Setting up GPU support..."
    
    # Apply GPU RuntimeClass and Device Plugin
    kubectl apply -f .\kubernetes\gpu-runtime-class.yaml
    
    Write-Success "GPU support configured"
    
    # Wait for NVIDIA Device Plugin to be ready
    Write-Status "Waiting for NVIDIA Device Plugin to be ready..."
    Start-Sleep -Seconds 30
    
    # Check GPU nodes
    Write-Status "Checking for GPU nodes..."
    kubectl get nodes -o custom-columns=NAME:.metadata.name,GPU:.status.allocatable.'nvidia\.com/gpu'
}

function Create-Namespaces {
    Write-Status "Creating MerlinHelm namespaces..."
    
    kubectl apply -f .\kubernetes\namespace.yaml
    
    Write-Success "Namespaces created"
}

function Test-ClusterSetup {
    Write-Status "Testing cluster setup..."
    
    # Check cluster info
    Write-Status "Cluster information:"
    kubectl cluster-info
    
    # Check nodes
    Write-Status "Cluster nodes:"
    kubectl get nodes -o wide
    
    # Check system pods
    Write-Status "System pods:"
    kubectl get pods -n kube-system
    
    # Check ingress
    if (kubectl get namespace ingress-nginx -o name 2>$null) {
        Write-Status "Ingress Controller pods:"
        kubectl get pods -n ingress-nginx
    }
    
    # Check cert-manager
    if (kubectl get namespace cert-manager -o name 2>$null) {
        Write-Status "cert-manager pods:"
        kubectl get pods -n cert-manager
    }
    
    Write-Success "Cluster setup test completed"
}

# Main execution
if ($Help) {
    Show-Usage
    exit 0
}

if (-not ($InstallIngress -or $InstallCertManager -or $SetupGPU -or $CreateNamespaces -or $All)) {
    Show-Usage
    exit 1
}

Write-Status "Starting MerlinHelm cluster setup..."

if ($All -or $InstallIngress) {
    Install-IngressController
}

if ($All -or $InstallCertManager) {
    Install-CertManager
}

if ($All -or $SetupGPU) {
    Setup-GPUSupport
}

if ($All -or $CreateNamespaces) {
    Create-Namespaces
}

Test-ClusterSetup

Write-Success "MerlinHelm cluster setup completed!"

Write-Status @"

Next Steps:
1. Verify GPU nodes are properly labeled and have GPU resources
2. Configure DNS for your ingress domains
3. Deploy MerlinHelm using: .\scripts\deploy.ps1 dev
4. Access OpenWebUI at the configured ingress URL

"@
