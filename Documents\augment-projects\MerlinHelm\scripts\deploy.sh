#!/bin/bash

# MerlinHelm Deployment Script
# Usage: ./deploy.sh [environment] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=""
NAMESPACE=""
DRY_RUN=false
UPGRADE=false
FORCE=false
CHART_PATH="./helm/merlin-stack"
VALUES_PATH=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
MerlinHelm Deployment Script

Usage: $0 [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
    local       Deploy to local environment (CPU-only)
    dev         Deploy to development environment
    test        Deploy to test environment
    prod        Deploy to production environment

OPTIONS:
    -n, --namespace NAMESPACE    Kubernetes namespace (default: merlinhelm-ENV)
    -d, --dry-run               Perform a dry run
    -u, --upgrade               Upgrade existing deployment
    -f, --force                 Force deployment (delete and recreate)
    -h, --help                  Show this help message

EXAMPLES:
    $0 dev                      Deploy to development
    $0 prod --upgrade           Upgrade production deployment
    $0 test --dry-run           Test deployment without applying changes

EOF
}

# Function to validate environment
validate_environment() {
    case $ENVIRONMENT in
        local|dev|test|prod)
            print_status "Environment: $ENVIRONMENT"
            ;;
        *)
            print_error "Invalid environment: $ENVIRONMENT"
            print_error "Valid environments: local, dev, test, prod"
            exit 1
            ;;
    esac
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    # Check if kubectl is installed and configured
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi

    # Check if helm is installed
    if ! command -v helm &> /dev/null; then
        print_error "helm is not installed or not in PATH"
        exit 1
    fi

    # Check if we can connect to Kubernetes cluster
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi

    # Check if chart directory exists
    if [ ! -d "$CHART_PATH" ]; then
        print_error "Chart directory not found: $CHART_PATH"
        exit 1
    fi

    print_success "Prerequisites check passed"
}

# Function to setup namespace
setup_namespace() {
    if [ -z "$NAMESPACE" ]; then
        NAMESPACE="merlinhelm-$ENVIRONMENT"
    fi

    print_status "Setting up namespace: $NAMESPACE"

    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        kubectl create namespace "$NAMESPACE"
        print_success "Created namespace: $NAMESPACE"
    else
        print_status "Namespace already exists: $NAMESPACE"
    fi
}

# Function to deploy with Helm
deploy_helm() {
    local helm_args=()

    # Set values file
    if [ -z "$VALUES_PATH" ]; then
        VALUES_PATH="./environments/$ENVIRONMENT/values.yaml"
    fi

    if [ ! -f "$VALUES_PATH" ]; then
        print_error "Values file not found: $VALUES_PATH"
        exit 1
    fi

    helm_args+=("--values" "$VALUES_PATH")
    helm_args+=("--namespace" "$NAMESPACE")

    if [ "$DRY_RUN" = true ]; then
        helm_args+=("--dry-run")
        print_status "Performing dry run..."
    fi

    local release_name="merlinhelm-$ENVIRONMENT"

    if helm list -n "$NAMESPACE" | grep -q "$release_name"; then
        if [ "$UPGRADE" = true ] || [ "$FORCE" = true ]; then
            if [ "$FORCE" = true ]; then
                print_warning "Force flag detected. Uninstalling existing release..."
                helm uninstall "$release_name" -n "$NAMESPACE"
                sleep 5
                helm install "$release_name" "$CHART_PATH" "${helm_args[@]}"
            else
                print_status "Upgrading existing release..."
                helm upgrade "$release_name" "$CHART_PATH" "${helm_args[@]}"
            fi
        else
            print_error "Release already exists. Use --upgrade or --force flag."
            exit 1
        fi
    else
        print_status "Installing new release..."
        helm install "$release_name" "$CHART_PATH" "${helm_args[@]}"
    fi

    if [ "$DRY_RUN" = false ]; then
        print_success "Deployment completed successfully!"

        # Show deployment status
        print_status "Checking deployment status..."
        kubectl get pods -n "$NAMESPACE"

        # Show services
        print_status "Services:"
        kubectl get svc -n "$NAMESPACE"

        # Show ingress if available
        if kubectl get ingress -n "$NAMESPACE" &> /dev/null; then
            print_status "Ingress:"
            kubectl get ingress -n "$NAMESPACE"
        fi
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        dev|test|prod)
            ENVIRONMENT="$1"
            shift
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -u|--upgrade)
            UPGRADE=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required arguments
if [ -z "$ENVIRONMENT" ]; then
    print_error "Environment is required"
    show_usage
    exit 1
fi

# Main execution
print_status "Starting MerlinHelm deployment..."
validate_environment
check_prerequisites
setup_namespace
deploy_helm

print_success "MerlinHelm deployment script completed!"
