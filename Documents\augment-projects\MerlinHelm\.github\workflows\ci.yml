name: <PERSON><PERSON> - <PERSON><PERSON> Chart Validation

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  HELM_VERSION: v3.12.0
  KUBERNETES_VERSION: v1.27.0

jobs:
  lint-and-validate:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Helm
      uses: azure/setup-helm@v3
      with:
        version: ${{ env.HELM_VERSION }}

    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: ${{ env.KUBERNETES_VERSION }}

    - name: Lint Helm charts
      run: |
        helm lint helm/ollama/
        helm lint helm/openwebui/
        helm lint helm/merlin-stack/

    - name: Validate Helm templates
      run: |
        # Validate Ollama chart
        helm template test-ollama helm/ollama/ \
          --values environments/dev/values.yaml \
          --validate

        # Validate OpenWebUI chart
        helm template test-openwebui helm/openwebui/ \
          --values environments/dev/values.yaml \
          --validate

        # Validate combined stack
        helm template test-stack helm/merlin-stack/ \
          --values environments/dev/values.yaml \
          --validate

    - name: Check Kubernetes manifests
      run: |
        # Generate manifests and validate with kubeval
        helm template test-stack helm/merlin-stack/ \
          --values environments/dev/values.yaml > manifests.yaml

        # Install kubeval
        wget https://github.com/instrumenta/kubeval/releases/latest/download/kubeval-linux-amd64.tar.gz
        tar xf kubeval-linux-amd64.tar.gz
        sudo mv kubeval /usr/local/bin

        # Validate manifests
        kubeval manifests.yaml

    - name: Security scan with Checkov
      uses: bridgecrewio/checkov-action@master
      with:
        directory: .
        framework: kubernetes,helm
        output_format: sarif
        output_file_path: reports/results.sarif

    - name: Upload Checkov results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: reports/results.sarif

  test-environments:
    runs-on: ubuntu-latest
    needs: lint-and-validate

    strategy:
      matrix:
        environment: [local, dev, test, prod]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Helm
      uses: azure/setup-helm@v3
      with:
        version: ${{ env.HELM_VERSION }}

    - name: Test ${{ matrix.environment }} environment
      run: |
        helm template test-${{ matrix.environment }} helm/merlin-stack/ \
          --values environments/${{ matrix.environment }}/values.yaml \
          --dry-run

    - name: Generate deployment manifests
      run: |
        mkdir -p artifacts/${{ matrix.environment }}
        helm template merlinhelm-${{ matrix.environment }} helm/merlin-stack/ \
          --values environments/${{ matrix.environment }}/values.yaml \
          --namespace merlinhelm-${{ matrix.environment }} > artifacts/${{ matrix.environment }}/manifests.yaml

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: manifests-${{ matrix.environment }}
        path: artifacts/${{ matrix.environment }}/

  dependency-check:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Helm
      uses: azure/setup-helm@v3
      with:
        version: ${{ env.HELM_VERSION }}

    - name: Update Helm dependencies
      run: |
        cd helm/merlin-stack
        helm dependency update

    - name: Check for outdated dependencies
      run: |
        # This would check for newer versions of dependencies
        # Implementation depends on your specific needs
        echo "Checking for outdated dependencies..."
