# Secrets Management Guide

This guide explains how to use the comprehensive secrets management system in MerlinHelm, which supports both local development and production deployments with external secret providers.

## Overview

MerlinHelm provides a flexible secrets management system that supports:

- **Local Secrets**: For development and testing environments
- **External Secrets**: For production environments using External Secrets Operator (ESO)
- **Multiple Providers**: GitHub Secrets, GitLab Variables, Azure Key Vault, AWS Secrets Manager, HashiCorp Vault

## Quick Start

### 1. Generate Local Secrets for Development

```powershell
# PowerShell
.\secrets\scripts\generate-secrets.ps1 -Environment dev

# Bash
./secrets/scripts/generate-secrets.sh dev
```

### 2. Deploy with Secrets

```powershell
# Development with local secrets
.\scripts\deploy.ps1 dev -GenerateSecrets

# Production with external secrets
.\scripts\deploy.ps1 prod -UseSecrets
```

### 3. Rotate Secrets

```powershell
# PowerShell
.\secrets\scripts\rotate-secrets.ps1 -Environment prod -Provider github

# Bash
./secrets/scripts/rotate-secrets.sh prod --provider=github
```

## Directory Structure

```
secrets/
├── README.md                    # Overview and usage
├── templates/                   # Templates and examples
│   ├── local-secrets.yaml.template
│   ├── external-secrets.yaml.template
│   └── github-secrets.md
├── local/                      # Local secrets (git-ignored)
│   ├── dev/secrets.yaml
│   ├── test/secrets.yaml
│   ├── prod/secrets.yaml
│   └── local/secrets.yaml
├── external/                   # External secrets configuration
│   ├── dev/external-secrets.yaml
│   ├── test/external-secrets.yaml
│   ├── prod/external-secrets.yaml
│   └── local/external-secrets.yaml
└── scripts/                    # Management scripts
    ├── generate-secrets.ps1/.sh
    ├── sync-secrets.ps1/.sh
    └── rotate-secrets.ps1/.sh
```

## Secret Types

### OpenWebUI Secrets
- `secretKey`: Session management secret
- `jwtSecret`: JWT authentication secret
- `databasePassword`: Database connection password
- `oauthClientSecret`: OAuth/OIDC client secret
- `smtpPassword`: SMTP server password
- `openaiApiKey`: OpenAI API key
- `anthropicApiKey`: Anthropic API key
- `googleApiKey`: Google API key
- `azureApiKey`: Azure API key

### Ollama Secrets
- `huggingfaceToken`: HuggingFace access token
- `registryPassword`: Private registry password

### Monitoring Secrets
- `prometheusPassword`: Prometheus admin password
- `grafanaAdminPassword`: Grafana admin password
- `datadogApiKey`: Datadog API key

### TLS Secrets
- `tlsCert`: TLS certificate (base64 encoded)
- `tlsKey`: TLS private key (base64 encoded)

## Environment Configuration

### Development Environment

For development, use local secrets:

```yaml
# environments/dev/values.yaml
openwebui:
  secret:
    create: true
    external:
      enabled: false
    local:
      enabled: true
    manual:
      openaiApiKey: "your-dev-api-key"
```

### Production Environment

For production, use external secrets:

```yaml
# environments/prod/values.yaml
openwebui:
  secret:
    create: true
    external:
      enabled: true
      secretStore: "merlinhelm-secret-store"
      secrets:
        openwebui:
          keys:
            secretKey: "MERLINHELM_PROD_OPENWEBUI_SECRET_KEY"
            jwtSecret: "MERLINHELM_PROD_OPENWEBUI_JWT_SECRET"
```

## External Secrets Setup

### 1. Install External Secrets Operator

```bash
helm repo add external-secrets https://charts.external-secrets.io
helm install external-secrets external-secrets/external-secrets -n external-secrets-system --create-namespace
```

### 2. Configure Secret Provider

#### GitHub Secrets

```yaml
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: merlinhelm-secret-store
spec:
  provider:
    github:
      auth:
        token:
          secretRef:
            name: github-token
            key: token
      owner: "your-github-org"
      repo: "your-repo"
```

#### GitLab Variables

```yaml
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: merlinhelm-secret-store
spec:
  provider:
    gitlab:
      auth:
        SecretRef:
          secretAccessToken:
            name: gitlab-token
            key: token
      projectID: "12345"
      url: "https://gitlab.com"
```

### 3. Create GitHub Token Secret

```bash
kubectl create secret generic github-token \
  --from-literal=token="your-github-token" \
  -n merlinhelm-prod
```

## Scripts Usage

### Generate Secrets

```powershell
# Generate all secrets for an environment
.\secrets\scripts\generate-secrets.ps1 -Environment dev

# Generate specific secret type
.\secrets\scripts\generate-secrets.ps1 -Environment dev -SecretType openwebui

# Generate with different output format
.\secrets\scripts\generate-secrets.ps1 -Environment dev -OutputFormat json
```

### Sync Secrets

```powershell
# Push local secrets to GitHub
.\secrets\scripts\sync-secrets.ps1 -Environment prod -Provider github -Direction push

# Pull secrets from remote (list only for GitHub)
.\secrets\scripts\sync-secrets.ps1 -Environment prod -Provider github -Direction pull

# Dry run to see what would be done
.\secrets\scripts\sync-secrets.ps1 -Environment prod -Provider github -DryRun
```

### Rotate Secrets

```powershell
# Rotate all secrets
.\secrets\scripts\rotate-secrets.ps1 -Environment prod

# Rotate specific secret type
.\secrets\scripts\rotate-secrets.ps1 -Environment prod -SecretType openwebui

# Rotate and sync to remote
.\secrets\scripts\rotate-secrets.ps1 -Environment prod -Provider github
```

## Security Best Practices

### 1. Environment Separation
- Use different secrets for each environment
- Never share production secrets with development
- Rotate secrets regularly (monthly/quarterly)

### 2. Access Control
- Use least privilege access for secret providers
- Monitor secret access and usage
- Implement approval workflows for production secrets

### 3. Storage Security
- Never commit secrets to version control
- Use encrypted storage for secret backups
- Implement secret scanning in CI/CD

### 4. Rotation Strategy
- Automate secret rotation where possible
- Test secret rotation in non-production first
- Have rollback procedures for failed rotations

## Troubleshooting

### Common Issues

#### 1. External Secrets Not Working
```bash
# Check External Secrets Operator
kubectl get pods -n external-secrets-system

# Check SecretStore status
kubectl describe secretstore merlinhelm-secret-store -n merlinhelm-prod

# Check ExternalSecret status
kubectl describe externalsecret merlinhelm-openwebui-secrets -n merlinhelm-prod
```

#### 2. Local Secrets Not Found
```bash
# Generate missing secrets
./secrets/scripts/generate-secrets.sh dev

# Check file permissions
ls -la secrets/local/dev/
```

#### 3. GitHub API Rate Limits
- Use a dedicated GitHub token with appropriate permissions
- Implement retry logic with exponential backoff
- Consider using GitHub Apps for higher rate limits

### Debugging Commands

```bash
# Check secret contents (be careful with sensitive data)
kubectl get secret openwebui-secret -n merlinhelm-dev -o yaml

# Test secret access from pod
kubectl exec -it <pod-name> -n merlinhelm-dev -- env | grep -i secret

# Check External Secrets Operator logs
kubectl logs -n external-secrets-system -l app.kubernetes.io/name=external-secrets
```

## Migration Guide

### From Manual to External Secrets

1. **Backup existing secrets**
2. **Set up External Secrets Operator**
3. **Configure secret provider**
4. **Update Helm values**
5. **Deploy and verify**
6. **Clean up manual secrets**

### From Local to External Secrets

1. **Export local secrets to remote provider**
2. **Configure External Secrets**
3. **Update environment values**
4. **Test deployment**
5. **Monitor for issues**

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review External Secrets Operator documentation
3. Check GitHub issues and discussions
4. Contact the MerlinHelm team
