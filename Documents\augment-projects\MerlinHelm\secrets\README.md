# Secrets Management

This directory contains the secrets management configuration for MerlinHelm.

## Directory Structure

```
secrets/
├── README.md                    # This file
├── templates/                   # Secret templates and examples
│   ├── local-secrets.yaml.template
│   ├── external-secrets.yaml.template
│   └── github-secrets.md
├── local/                      # Environment-specific local secrets (git-ignored)
│   ├── dev/
│   ├── test/
│   ├── prod/
│   └── local/
├── external/                   # External secrets configuration
│   ├── dev/
│   ├── test/
│   ├── prod/
│   └── local/
└── scripts/                    # Secrets management scripts
    ├── generate-secrets.ps1
    ├── generate-secrets.sh
    ├── sync-secrets.ps1
    ├── sync-secrets.sh
    ├── rotate-secrets.ps1
    └── rotate-secrets.sh
```

## Secret Types

### Local Secrets
- Stored locally in git-ignored directories
- Used for development and testing
- Generated automatically or manually configured

### External Secrets
- Stored in external systems (GitHub Secrets, GitLab Variables, etc.)
- Used for production and CI/CD
- Managed through External Secrets Operator (ESO)

## Usage

### 1. Initialize Secrets for an Environment
```powershell
# PowerShell
.\secrets\scripts\generate-secrets.ps1 -Environment dev

# Bash
./secrets/scripts/generate-secrets.sh dev
```

### 2. Sync Secrets to Remote
```powershell
# PowerShell
.\secrets\scripts\sync-secrets.ps1 -Environment prod -Provider github

# Bash
./secrets/scripts/sync-secrets.sh prod github
```

### 3. Deploy with Secrets
```powershell
# PowerShell
.\scripts\deploy.ps1 dev -UseSecrets

# Bash
./scripts/deploy.sh dev --use-secrets
```

## Security Best Practices

1. **Never commit actual secrets to git**
2. **Use different secrets for each environment**
3. **Rotate secrets regularly**
4. **Use least privilege access**
5. **Monitor secret access and usage**
6. **Backup secrets securely**

## Supported Secret Providers

- **Local Files**: For development and testing
- **GitHub Secrets**: For GitHub Actions CI/CD
- **GitLab Variables**: For GitLab CI/CD
- **Azure Key Vault**: For Azure-based deployments
- **AWS Secrets Manager**: For AWS-based deployments
- **HashiCorp Vault**: For enterprise secret management

## Environment Variables

The following environment variables are used by the secrets management system:

- `MERLINHELM_SECRETS_PROVIDER`: Secret provider (local, github, gitlab, azure, aws, vault)
- `MERLINHELM_SECRETS_PATH`: Path to secrets directory
- `GITHUB_TOKEN`: GitHub token for GitHub Secrets API
- `GITLAB_TOKEN`: GitLab token for GitLab Variables API

## Quick Examples

### Generate Secrets for Development
```powershell
# PowerShell
.\scripts\generate-secrets.ps1 -Environment dev

# Bash
./scripts/generate-secrets.sh dev
```

### Deploy with Generated Secrets
```powershell
# PowerShell
..\scripts\deploy.ps1 dev -GenerateSecrets

# Bash
../scripts/deploy.sh dev --generate-secrets
```

### Rotate Production Secrets
```powershell
# PowerShell
.\scripts\rotate-secrets.ps1 -Environment prod -Provider github -BackupOld

# Bash
./scripts/rotate-secrets.sh prod --provider=github --backup
```

### Sync Secrets to GitHub
```powershell
# PowerShell
.\scripts\sync-secrets.ps1 -Environment prod -Provider github -Direction push

# Bash
./scripts/sync-secrets.sh prod github push
```

## Integration with Deployment

The secrets management system is integrated with the main deployment scripts:

```powershell
# Deploy development with auto-generated secrets
.\scripts\deploy.ps1 dev -GenerateSecrets

# Deploy production with external secrets
.\scripts\deploy.ps1 prod -UseSecrets
```

## File Structure Details

### Local Secrets (`local/`)
- Git-ignored directory containing environment-specific secrets
- Used for development and testing
- Generated automatically or manually configured

### External Secrets (`external/`)
- Configuration for External Secrets Operator
- Defines how to fetch secrets from external providers
- Version controlled (no sensitive data)

### Templates (`templates/`)
- Example configurations and documentation
- Starting points for new environments
- Reference for secret structure

### Scripts (`scripts/`)
- Automation tools for secret management
- Available in both PowerShell and Bash
- Handles generation, rotation, and synchronization
