#!/bin/bash
# Rotate secrets for MerlinHelm deployment
#
# Usage: ./rotate-secrets.sh <environment> [secret-type] [options]
#
# Examples:
#   ./rotate-secrets.sh dev
#   ./rotate-secrets.sh prod openwebui --backup
#   ./rotate-secrets.sh test --provider=github --force

set -euo pipefail

# Default values
ENVIRONMENT=""
SECRET_TYPE=""
PROVIDER=""
BACKUP_OLD=false
FORCE=false

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SECRETS_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${CYAN}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

print_gray() {
    echo -e "${GRAY}$1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 <environment> [secret-type] [options]

Arguments:
  environment    Environment to rotate secrets for (dev, test, prod, local)
  secret-type    Specific secret type to rotate (optional)
                 Options: openwebui, ollama, monitoring, tls, backup, custom

Options:
  --backup       Create backup of old secrets before rotation
  --provider=P   Remote provider to sync rotated secrets to
  --force        Skip confirmation prompts
  --help         Show this help message

Examples:
  $0 dev
  $0 prod openwebui --backup
  $0 test --provider=github --force
EOF
}

# Function to generate random string
generate_random_string() {
    local length=${1:-32}
    local chars=${2:-"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"}
    
    if command -v openssl >/dev/null 2>&1; then
        openssl rand -base64 $((length * 3 / 4)) | tr -d "=+/" | cut -c1-${length}
    else
        < /dev/urandom tr -dc "$chars" | head -c${length}
    fi
}

# Function to generate password
generate_password() {
    local length=${1:-16}
    local chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*"
    generate_random_string "$length" "$chars"
}

# Function to generate JWT secret
generate_jwt_secret() {
    local length=${1:-64}
    local chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
    generate_random_string "$length" "$chars"
}

# Function to create backup
backup_secrets() {
    local source_file="$1"
    local timestamp=$(date +"%Y%m%d-%H%M%S")
    local backup_file="${source_file}.backup-${timestamp}"
    
    cp "$source_file" "$backup_file"
    print_info "📦 Backup created: $backup_file"
    echo "$backup_file"
}

# Function to rotate secrets in file
rotate_secrets_in_file() {
    local file="$1"
    local secret_type="$2"
    local temp_file=$(mktemp)
    local in_target_section=false
    local current_section=""
    
    while IFS= read -r line; do
        local trimmed_line=$(echo "$line" | sed 's/^[[:space:]]*//')
        
        # Check if we're entering a new section
        if [[ "$trimmed_line" =~ ^([a-zA-Z0-9_]+):$ ]]; then
            current_section="${BASH_REMATCH[1]}"
            if [[ -n "$secret_type" ]]; then
                in_target_section=$([[ "$secret_type" == "$current_section" ]] && echo true || echo false)
            else
                in_target_section=true
            fi
        fi
        
        # If we're in the target section and this is a secret value, rotate it
        if [[ "$in_target_section" == true && "$trimmed_line" =~ ^([[:space:]]*)([a-zA-Z0-9_]+):[[:space:]]*\"(.+)\"$ ]]; then
            local indent="${BASH_REMATCH[1]}"
            local key="${BASH_REMATCH[2]}"
            local old_value="${BASH_REMATCH[3]}"
            
            # Skip empty values and certain keys that shouldn't be rotated
            if [[ -z "$old_value" ]] || [[ "$key" =~ ^(username|host|port|name|bucket|region|endpoint|fromEmail|issuerUrl|server|containerName)$ ]]; then
                echo "$line" >> "$temp_file"
                continue
            fi
            
            # Generate new value based on key type
            local new_value
            case "$key" in
                *password*|*Password*)
                    new_value=$(generate_password 16)
                    ;;
                *secret*|*Secret*)
                    new_value=$(generate_random_string 32)
                    ;;
                *jwt*|*Jwt*|*JWT*)
                    new_value=$(generate_jwt_secret 64)
                    ;;
                *key*|*Key*)
                    new_value=$(generate_random_string 40)
                    ;;
                *token*|*Token*)
                    new_value=$(generate_random_string 64)
                    ;;
                *)
                    new_value=$(generate_random_string 32)
                    ;;
            esac
            
            echo "${indent}${key}: \"${new_value}\"" >> "$temp_file"
            print_warning "  🔄 Rotated: $key"
        else
            echo "$line" >> "$temp_file"
        fi
    done < "$file"
    
    # Update rotation timestamp
    sed -i 's/# Generated on: .*/# Last rotated on: '"$(date -u +"%Y-%m-%d %H:%M:%S")"'/' "$temp_file"
    
    mv "$temp_file" "$file"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --backup)
            BACKUP_OLD=true
            shift
            ;;
        --provider=*)
            PROVIDER="${1#*=}"
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        -*)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
        *)
            if [[ -z "$ENVIRONMENT" ]]; then
                ENVIRONMENT="$1"
            elif [[ -z "$SECRET_TYPE" ]]; then
                SECRET_TYPE="$1"
            else
                print_error "Too many arguments"
                show_usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required arguments
if [[ -z "$ENVIRONMENT" ]]; then
    print_error "Environment is required"
    show_usage
    exit 1
fi

# Validate environment
case "$ENVIRONMENT" in
    dev|test|prod|local)
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT"
        print_error "Valid environments: dev, test, prod, local"
        exit 1
        ;;
esac

# Validate secret type if provided
if [[ -n "$SECRET_TYPE" ]]; then
    case "$SECRET_TYPE" in
        openwebui|ollama|monitoring|tls|backup|custom)
            ;;
        *)
            print_error "Invalid secret type: $SECRET_TYPE"
            print_error "Valid types: openwebui, ollama, monitoring, tls, backup, custom"
            exit 1
            ;;
    esac
fi

# Validate provider if provided
if [[ -n "$PROVIDER" ]]; then
    case "$PROVIDER" in
        github|gitlab|azure|aws|vault)
            ;;
        *)
            print_error "Invalid provider: $PROVIDER"
            print_error "Valid providers: github, gitlab, azure, aws, vault"
            exit 1
            ;;
    esac
fi

# Check if local secrets file exists
local_secrets_file="$SECRETS_DIR/local/$ENVIRONMENT/secrets.yaml"
if [[ ! -f "$local_secrets_file" ]]; then
    print_error "Local secrets file not found: $local_secrets_file"
    print_warning "Run generate-secrets.sh first to create local secrets"
    exit 1
fi

# Main execution
echo -e "${MAGENTA}🔄 MerlinHelm Secrets Rotation${NC}"
echo -e "${MAGENTA}==============================${NC}"
print_info "Environment: $ENVIRONMENT"

if [[ -n "$SECRET_TYPE" ]]; then
    print_info "Secret Type: $SECRET_TYPE"
fi

if [[ -n "$PROVIDER" ]]; then
    print_info "Provider: $PROVIDER"
fi

# Confirmation prompt
if [[ "$FORCE" != true ]]; then
    echo ""
    print_warning "⚠️  WARNING: This will regenerate secrets and may cause service disruption!"
    print_warning "Make sure to update all services that use these secrets after rotation."
    
    echo ""
    read -p "Do you want to continue? (y/N): " confirmation
    if [[ ! "$confirmation" =~ ^[Yy]$ ]]; then
        print_error "❌ Operation cancelled"
        exit 0
    fi
fi

# Create backup if requested
if [[ "$BACKUP_OLD" == true ]]; then
    echo ""
    print_info "📦 Creating backup..."
    backup_file=$(backup_secrets "$local_secrets_file")
fi

# Read current secrets
echo ""
print_info "📖 Reading current secrets..."

# Rotate secrets
echo ""
print_info "🔄 Rotating secrets..."

if [[ -n "$SECRET_TYPE" ]]; then
    print_warning "Rotating $SECRET_TYPE secrets:"
else
    print_warning "Rotating all secrets:"
fi

rotate_secrets_in_file "$local_secrets_file" "$SECRET_TYPE"

echo ""
print_success "✅ Secrets rotated successfully!"

# Sync to remote provider if specified
if [[ -n "$PROVIDER" ]]; then
    echo ""
    print_info "🔄 Syncing to remote provider..."
    
    sync_script="$SCRIPT_DIR/sync-secrets.sh"
    if [[ -f "$sync_script" ]]; then
        if "$sync_script" "$ENVIRONMENT" "$PROVIDER" push; then
            print_success "✅ Secrets synced to $PROVIDER"
        else
            print_warning "❌ Failed to sync secrets to $PROVIDER"
        fi
    else
        print_warning "Sync script not found: $sync_script"
    fi
fi

echo ""
print_info "📋 Next Steps:"
print_gray "1. Update any services that use these secrets"
print_gray "2. Restart affected deployments"
print_gray "3. Verify all services are working correctly"

if [[ "$BACKUP_OLD" == true ]]; then
    print_gray "4. Remove backup file when no longer needed: $backup_file"
fi

echo ""
print_warning "⚠️  Security Reminder:"
print_warning "- Monitor services for any authentication failures"
print_warning "- Update any external systems that use these secrets"
print_warning "- Consider rotating secrets regularly (monthly/quarterly)"
