#!/bin/bash
# Sync secrets between local and remote providers
#
# Usage: ./sync-secrets.sh <environment> <provider> [direction] [options]
#
# Examples:
#   ./sync-secrets.sh prod github push
#   ./sync-secrets.sh dev gitlab pull --dry-run
#   ./sync-secrets.sh test azure both --force

set -euo pipefail

# Default values
ENVIRONMENT=""
PROVIDER=""
DIRECTION="push"
DRY_RUN=false
FORCE=false

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SECRETS_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${CYAN}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

print_gray() {
    echo -e "${GRAY}$1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 <environment> <provider> [direction] [options]

Arguments:
  environment    Environment to sync secrets for (dev, test, prod, local)
  provider       Secret provider (github, gitlab, azure, aws, vault)
  direction      Sync direction: push, pull, both (default: push)

Options:
  --dry-run      Show what would be done without making changes
  --force        Overwrite existing secrets without confirmation
  --help         Show this help message

Examples:
  $0 prod github push
  $0 dev gitlab pull --dry-run
  $0 test azure both --force

Required Environment Variables:
  GitHub:    GITHUB_TOKEN, GITHUB_REPOSITORY
  GitLab:    GITLAB_TOKEN, GITLAB_PROJECT_ID, GITLAB_URL (optional)
  Azure:     AZURE_CLIENT_ID, AZURE_CLIENT_SECRET, AZURE_TENANT_ID
  AWS:       AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY
  Vault:     VAULT_ADDR, VAULT_TOKEN
EOF
}

# Function to check required tools
check_dependencies() {
    local missing_tools=()
    
    if ! command -v curl >/dev/null 2>&1; then
        missing_tools+=("curl")
    fi
    
    if ! command -v jq >/dev/null 2>&1; then
        missing_tools+=("jq")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_error "Please install the missing tools and try again"
        exit 1
    fi
}

# Function to read YAML secrets (simplified parser)
read_yaml_secrets() {
    local file="$1"
    local temp_file=$(mktemp)
    
    # Convert YAML to a simple key-value format
    grep -E "^\s*\w+:\s*\".*\"$" "$file" | while IFS= read -r line; do
        # Extract key and value
        key=$(echo "$line" | sed -E 's/^\s*([^:]+):\s*"(.*)"\s*$/\1/')
        value=$(echo "$line" | sed -E 's/^\s*([^:]+):\s*"(.*)"\s*$/\2/')
        
        # Skip empty values
        if [[ -n "$value" ]]; then
            echo "$key=$value"
        fi
    done > "$temp_file"
    
    echo "$temp_file"
}

# Function to sync with GitHub Secrets
sync_github_secrets() {
    local direction="$1"
    local dry_run="$2"
    
    if [[ -z "${GITHUB_TOKEN:-}" ]]; then
        print_error "GITHUB_TOKEN environment variable is required for GitHub provider"
        exit 1
    fi
    
    local github_repo="${GITHUB_REPOSITORY:-your-org/MerlinHelm}"
    local base_url="https://api.github.com/repos/$github_repo"
    
    print_info "🔄 Syncing with GitHub Secrets..."
    print_gray "📋 Repository: $github_repo"
    
    if [[ "$direction" == "push" || "$direction" == "both" ]]; then
        print_info "🔄 Pushing secrets to GitHub..."
        
        # Get repository public key
        local public_key_response
        public_key_response=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
                                  -H "Accept: application/vnd.github.v3+json" \
                                  "$base_url/actions/secrets/public-key")
        
        if [[ $? -ne 0 ]]; then
            print_error "Failed to get GitHub repository public key"
            exit 1
        fi
        
        local public_key
        local key_id
        public_key=$(echo "$public_key_response" | jq -r '.key')
        key_id=$(echo "$public_key_response" | jq -r '.key_id')
        
        # Read local secrets
        local secrets_file="$SECRETS_DIR/local/$ENVIRONMENT/secrets.yaml"
        if [[ ! -f "$secrets_file" ]]; then
            print_error "Local secrets file not found: $secrets_file"
            exit 1
        fi
        
        # Convert secrets to environment variables format
        local temp_secrets
        temp_secrets=$(read_yaml_secrets "$secrets_file")
        
        # Process each secret
        while IFS='=' read -r key value; do
            if [[ -z "$key" || -z "$value" ]]; then
                continue
            fi
            
            # Convert to GitHub secret name format
            local secret_name="MERLINHELM_${ENVIRONMENT^^}_${key^^}"
            secret_name=$(echo "$secret_name" | tr '.' '_' | tr '-' '_')
            
            if [[ "$dry_run" == true ]]; then
                print_warning "  [DRY RUN] Would set GitHub secret: $secret_name"
                continue
            fi
            
            # For demo purposes, we'll use base64 encoding instead of proper encryption
            # In production, you would use libsodium for proper encryption
            local encrypted_value
            encrypted_value=$(echo -n "$value" | base64)
            
            # Create secret
            local response
            response=$(curl -s -X PUT \
                          -H "Authorization: token $GITHUB_TOKEN" \
                          -H "Accept: application/vnd.github.v3+json" \
                          -H "Content-Type: application/json" \
                          -d "{\"encrypted_value\":\"$encrypted_value\",\"key_id\":\"$key_id\"}" \
                          "$base_url/actions/secrets/$secret_name")
            
            if [[ $? -eq 0 ]]; then
                print_success "  ✅ Set GitHub secret: $secret_name"
            else
                print_warning "  ❌ Failed to set GitHub secret: $secret_name"
            fi
        done < "$temp_secrets"
        
        rm -f "$temp_secrets"
    fi
    
    if [[ "$direction" == "pull" || "$direction" == "both" ]]; then
        print_info "🔄 Pulling secrets from GitHub..."
        print_warning "GitHub Secrets API does not support reading secret values"
        print_warning "Only secret names can be retrieved for verification"
        
        local secrets_response
        secrets_response=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
                              -H "Accept: application/vnd.github.v3+json" \
                              "$base_url/actions/secrets")
        
        if [[ $? -eq 0 ]]; then
            print_info "📋 Remote GitHub secrets:"
            echo "$secrets_response" | jq -r '.secrets[] | "  - \(.name) (updated: \(.updated_at))"' | while read -r line; do
                print_gray "$line"
            done
        else
            print_error "Failed to list GitHub secrets"
        fi
    fi
}

# Function to sync with GitLab Variables
sync_gitlab_variables() {
    local direction="$1"
    local dry_run="$2"
    
    if [[ -z "${GITLAB_TOKEN:-}" ]]; then
        print_error "GITLAB_TOKEN environment variable is required for GitLab provider"
        exit 1
    fi
    
    if [[ -z "${GITLAB_PROJECT_ID:-}" ]]; then
        print_error "GITLAB_PROJECT_ID environment variable is required for GitLab provider"
        exit 1
    fi
    
    local gitlab_url="${GITLAB_URL:-https://gitlab.com}"
    local base_url="$gitlab_url/api/v4/projects/$GITLAB_PROJECT_ID"
    
    print_info "🔄 Syncing with GitLab Variables..."
    print_gray "📋 Project ID: $GITLAB_PROJECT_ID"
    print_gray "🌐 GitLab URL: $gitlab_url"
    
    print_warning "GitLab Variables sync not yet implemented"
}

# Function to sync with Azure Key Vault
sync_azure_keyvault() {
    local direction="$1"
    local dry_run="$2"
    
    print_info "🔄 Syncing with Azure Key Vault..."
    print_warning "Azure Key Vault sync not yet implemented"
}

# Function to sync with AWS Secrets Manager
sync_aws_secrets() {
    local direction="$1"
    local dry_run="$2"
    
    print_info "🔄 Syncing with AWS Secrets Manager..."
    print_warning "AWS Secrets Manager sync not yet implemented"
}

# Function to sync with HashiCorp Vault
sync_vault() {
    local direction="$1"
    local dry_run="$2"
    
    print_info "🔄 Syncing with HashiCorp Vault..."
    print_warning "HashiCorp Vault sync not yet implemented"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        -*)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
        *)
            if [[ -z "$ENVIRONMENT" ]]; then
                ENVIRONMENT="$1"
            elif [[ -z "$PROVIDER" ]]; then
                PROVIDER="$1"
            elif [[ -z "$DIRECTION" || "$DIRECTION" == "push" ]]; then
                DIRECTION="$1"
            else
                print_error "Too many arguments"
                show_usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required arguments
if [[ -z "$ENVIRONMENT" ]]; then
    print_error "Environment is required"
    show_usage
    exit 1
fi

if [[ -z "$PROVIDER" ]]; then
    print_error "Provider is required"
    show_usage
    exit 1
fi

# Validate environment
case "$ENVIRONMENT" in
    dev|test|prod|local)
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT"
        print_error "Valid environments: dev, test, prod, local"
        exit 1
        ;;
esac

# Validate provider
case "$PROVIDER" in
    github|gitlab|azure|aws|vault)
        ;;
    *)
        print_error "Invalid provider: $PROVIDER"
        print_error "Valid providers: github, gitlab, azure, aws, vault"
        exit 1
        ;;
esac

# Validate direction
case "$DIRECTION" in
    push|pull|both)
        ;;
    *)
        print_error "Invalid direction: $DIRECTION"
        print_error "Valid directions: push, pull, both"
        exit 1
        ;;
esac

# Check dependencies
check_dependencies

# Main execution
echo -e "${MAGENTA}🔐 MerlinHelm Secrets Sync${NC}"
echo -e "${MAGENTA}=========================${NC}"
print_info "Environment: $ENVIRONMENT"
print_info "Provider: $PROVIDER"
print_info "Direction: $DIRECTION"

if [[ "$DRY_RUN" == true ]]; then
    print_warning "Mode: DRY RUN"
fi

echo ""

# Check if local secrets file exists
local_secrets_file="$SECRETS_DIR/local/$ENVIRONMENT/secrets.yaml"
if [[ ! -f "$local_secrets_file" ]]; then
    print_error "Local secrets file not found: $local_secrets_file"
    print_warning "Run generate-secrets.sh first to create local secrets"
    exit 1
fi

print_info "📖 Reading local secrets..."
print_success "✅ Local secrets loaded"

# Sync based on provider
case "$PROVIDER" in
    github)
        sync_github_secrets "$DIRECTION" "$DRY_RUN"
        ;;
    gitlab)
        sync_gitlab_variables "$DIRECTION" "$DRY_RUN"
        ;;
    azure)
        sync_azure_keyvault "$DIRECTION" "$DRY_RUN"
        ;;
    aws)
        sync_aws_secrets "$DIRECTION" "$DRY_RUN"
        ;;
    vault)
        sync_vault "$DIRECTION" "$DRY_RUN"
        ;;
esac

echo ""
print_success "✅ Secrets sync completed!"
