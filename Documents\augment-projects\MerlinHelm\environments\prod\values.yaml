# Production environment values
# Optimized for high availability and performance

global:
  environment: prod
  domain: merlinhelm.com
  storageClass: "premium-ssd"

ollama:
  enabled: true
  replicaCount: 3

  image:
    tag: "0.1.17"  # Use specific version in prod
    pullPolicy: IfNotPresent

  resources:
    limits:
      nvidia.com/gpu: 2
      memory: 16Gi
      cpu: 4000m
    requests:
      memory: 8Gi
      cpu: 2000m

  persistence:
    enabled: true
    size: 500Gi
    storageClass: "premium-ssd"

  nodeSelector:
    accelerator: nvidia-tesla-a100
    node-type: gpu

  tolerations:
    - key: nvidia.com/gpu
      operator: Exists
      effect: NoSchedule
    - key: node-type
      operator: Equal
      value: gpu
      effect: NoSchedule

  service:
    type: ClusterIP
    port: 11434

  models:
    preload:
      - llama2:7b
      - llama2:13b
      - llama2:70b
      - codellama:7b
      - codellama:13b
      - mistral:7b

  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 60
    targetMemoryUtilizationPercentage: 70

  # GPU configuration for production
  gpu:
    enabled: true

openwebui:
  enabled: true
  replicaCount: 3

  image:
    tag: "0.1.105"  # Use specific version in prod
    pullPolicy: IfNotPresent

  resources:
    limits:
      memory: 4Gi
      cpu: 2000m
    requests:
      memory: 1Gi
      cpu: 500m

  persistence:
    enabled: true
    size: 100Gi
    storageClass: "premium-ssd"

  service:
    type: ClusterIP
    port: 8080

  ingress:
    enabled: true
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
      nginx.ingress.kubernetes.io/rate-limit: "100"
      nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    hosts:
      - host: openwebui.merlinhelm.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: openwebui-prod-tls
        hosts:
          - openwebui.merlinhelm.com

  config:
    ollamaBaseUrl: "http://merlinhelm-prod-ollama:11434"
    enableSignup: false  # Disable signup in prod
    defaultUserRole: "pending"
    webUIName: "MerlinHelm Production WebUI"
    enableImageGeneration: true
    enableCommunitySharing: false

  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 60
    targetMemoryUtilizationPercentage: 70

monitoring:
  enabled: true
  prometheus:
    enabled: true
    retention: "30d"
    storageSize: "100Gi"
  grafana:
    enabled: true
    persistence:
      enabled: true
      size: "10Gi"

networkPolicies:
  enabled: true

podDisruptionBudget:
  enabled: true
  minAvailable: 2
