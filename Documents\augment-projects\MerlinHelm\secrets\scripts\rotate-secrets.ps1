#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Rotate secrets for MerlinHelm deployment

.DESCRIPTION
    This script rotates (regenerates) secrets for a specified environment.
    It can rotate all secrets or specific ones, and optionally sync to remote providers.

.PARAMETER Environment
    The environment to rotate secrets for (dev, test, prod, local)

.PARAMETER SecretType
    Specific secret type to rotate (optional, rotates all if not specified)

.PARAMETER Provider
    Remote provider to sync rotated secrets to (optional)

.PARAMETER BackupOld
    Create backup of old secrets before rotation

.PARAMETER Force
    Skip confirmation prompts

.EXAMPLE
    .\rotate-secrets.ps1 -Environment dev
    .\rotate-secrets.ps1 -Environment prod -SecretType openwebui -BackupOld
    .\rotate-secrets.ps1 -Environment test -Provider github -Force

.NOTES
    Author: MerlinHelm Team
    Version: 1.0.0
#>

param(
    [Parameter(Mandatory = $true)]
    [ValidateSet("dev", "test", "prod", "local")]
    [string]$Environment,
    
    [Parameter(Mandatory = $false)]
    [ValidateSet("openwebui", "ollama", "monitoring", "tls", "backup", "custom")]
    [string]$SecretType,
    
    [Parameter(Mandatory = $false)]
    [ValidateSet("github", "gitlab", "azure", "aws", "vault")]
    [string]$Provider,
    
    [Parameter(Mandatory = $false)]
    [switch]$BackupOld,
    
    [Parameter(Mandatory = $false)]
    [switch]$Force
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$SecretsDir = Split-Path -Parent $ScriptDir
$LocalSecretsDir = Join-Path $SecretsDir "local" $Environment

# Check if local secrets file exists
$LocalSecretsFile = Join-Path $LocalSecretsDir "secrets.yaml"
if (-not (Test-Path $LocalSecretsFile)) {
    Write-Error "Local secrets file not found: $LocalSecretsFile"
    Write-Host "Run generate-secrets.ps1 first to create local secrets" -ForegroundColor Yellow
    exit 1
}

# Function to create backup
function Backup-Secrets {
    param([string]$SourceFile)
    
    $Timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $BackupFile = "$SourceFile.backup-$Timestamp"
    
    Copy-Item $SourceFile $BackupFile
    Write-Host "📦 Backup created: $BackupFile" -ForegroundColor Cyan
    return $BackupFile
}

# Function to generate random string
function New-RandomString {
    param(
        [int]$Length = 32,
        [string]$Characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    )
    
    $Random = New-Object System.Random
    $Result = ""
    for ($i = 0; $i -lt $Length; $i++) {
        $Result += $Characters[$Random.Next(0, $Characters.Length)]
    }
    return $Result
}

# Function to generate password
function New-Password {
    param([int]$Length = 16)
    $Characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*"
    return New-RandomString -Length $Length -Characters $Characters
}

# Function to generate JWT secret
function New-JwtSecret {
    param([int]$Length = 64)
    $Characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
    return New-RandomString -Length $Length -Characters $Characters
}

# Function to rotate specific secret types
function Rotate-SecretType {
    param(
        [string]$Content,
        [string]$Type
    )
    
    $Lines = $Content -split "`n"
    $NewLines = @()
    $InTargetSection = $false
    $CurrentSection = ""
    
    foreach ($Line in $Lines) {
        $TrimmedLine = $Line.Trim()
        
        # Check if we're entering a new section
        if ($TrimmedLine -match "^(\w+):$") {
            $CurrentSection = $Matches[1]
            $InTargetSection = ($Type -eq $CurrentSection)
        }
        
        # If we're in the target section and this is a secret value, rotate it
        if ($InTargetSection -and $TrimmedLine -match '^(\s*)(\w+): "(.+)"$') {
            $Indent = $Matches[1]
            $Key = $Matches[2]
            $OldValue = $Matches[3]
            
            # Skip empty values and certain keys that shouldn't be rotated
            if ($OldValue -eq "" -or $Key -in @("username", "host", "port", "name", "bucket", "region", "endpoint", "fromEmail", "issuerUrl", "server", "containerName")) {
                $NewLines += $Line
                continue
            }
            
            # Generate new value based on key type
            $NewValue = switch -Regex ($Key) {
                "password|Password" { New-Password -Length 16 }
                "secret|Secret" { New-RandomString -Length 32 }
                "jwt|Jwt|JWT" { New-JwtSecret -Length 64 }
                "key|Key" { New-RandomString -Length 40 }
                "token|Token" { New-RandomString -Length 64 }
                default { New-RandomString -Length 32 }
            }
            
            $NewLine = "$Indent$Key`: `"$NewValue`""
            $NewLines += $NewLine
            Write-Host "  🔄 Rotated: $Key" -ForegroundColor Yellow
        }
        else {
            $NewLines += $Line
        }
    }
    
    return ($NewLines -join "`n")
}

# Function to rotate all secrets
function Rotate-AllSecrets {
    param([string]$Content)
    
    $Lines = $Content -split "`n"
    $NewLines = @()
    
    foreach ($Line in $Lines) {
        $TrimmedLine = $Line.Trim()
        
        # If this is a secret value, rotate it
        if ($TrimmedLine -match '^(\s*)(\w+): "(.+)"$') {
            $Indent = $Matches[1]
            $Key = $Matches[2]
            $OldValue = $Matches[3]
            
            # Skip empty values and certain keys that shouldn't be rotated
            if ($OldValue -eq "" -or $Key -in @("username", "host", "port", "name", "bucket", "region", "endpoint", "fromEmail", "issuerUrl", "server", "containerName")) {
                $NewLines += $Line
                continue
            }
            
            # Generate new value based on key type
            $NewValue = switch -Regex ($Key) {
                "password|Password" { New-Password -Length 16 }
                "secret|Secret" { New-RandomString -Length 32 }
                "jwt|Jwt|JWT" { New-JwtSecret -Length 64 }
                "key|Key" { New-RandomString -Length 40 }
                "token|Token" { New-RandomString -Length 64 }
                default { New-RandomString -Length 32 }
            }
            
            $NewLine = "$Indent$Key`: `"$NewValue`""
            $NewLines += $NewLine
            Write-Host "  🔄 Rotated: $Key" -ForegroundColor Yellow
        }
        else {
            $NewLines += $Line
        }
    }
    
    return ($NewLines -join "`n")
}

# Main execution
Write-Host "🔄 MerlinHelm Secrets Rotation" -ForegroundColor Magenta
Write-Host "==============================" -ForegroundColor Magenta
Write-Host "Environment: $Environment" -ForegroundColor Cyan

if ($SecretType) {
    Write-Host "Secret Type: $SecretType" -ForegroundColor Cyan
}

if ($Provider) {
    Write-Host "Provider: $Provider" -ForegroundColor Cyan
}

# Confirmation prompt
if (-not $Force) {
    Write-Host "`n⚠️  WARNING: This will regenerate secrets and may cause service disruption!" -ForegroundColor Yellow
    Write-Host "Make sure to update all services that use these secrets after rotation." -ForegroundColor Yellow
    
    $Confirmation = Read-Host "`nDo you want to continue? (y/N)"
    if ($Confirmation -notmatch "^[Yy]$") {
        Write-Host "❌ Operation cancelled" -ForegroundColor Red
        exit 0
    }
}

# Create backup if requested
if ($BackupOld) {
    Write-Host "`n📦 Creating backup..." -ForegroundColor Cyan
    $BackupFile = Backup-Secrets -SourceFile $LocalSecretsFile
}

# Read current secrets
Write-Host "`n📖 Reading current secrets..." -ForegroundColor Cyan
$CurrentContent = Get-Content $LocalSecretsFile -Raw

# Rotate secrets
Write-Host "`n🔄 Rotating secrets..." -ForegroundColor Cyan

if ($SecretType) {
    Write-Host "Rotating $SecretType secrets:" -ForegroundColor Yellow
    $NewContent = Rotate-SecretType -Content $CurrentContent -Type $SecretType
}
else {
    Write-Host "Rotating all secrets:" -ForegroundColor Yellow
    $NewContent = Rotate-AllSecrets -Content $CurrentContent
}

# Add rotation timestamp comment
$Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
$NewContent = $NewContent -replace "# Generated on: .*", "# Last rotated on: $Timestamp"

# Save rotated secrets
Write-Host "`n💾 Saving rotated secrets..." -ForegroundColor Cyan
$NewContent | Out-File -FilePath $LocalSecretsFile -Encoding UTF8

Write-Host "✅ Secrets rotated successfully!" -ForegroundColor Green

# Sync to remote provider if specified
if ($Provider) {
    Write-Host "`n🔄 Syncing to remote provider..." -ForegroundColor Cyan
    
    $SyncScript = Join-Path $ScriptDir "sync-secrets.ps1"
    if (Test-Path $SyncScript) {
        try {
            & $SyncScript -Environment $Environment -Provider $Provider -Direction push
            Write-Host "✅ Secrets synced to $Provider" -ForegroundColor Green
        }
        catch {
            Write-Warning "❌ Failed to sync secrets to $Provider`: $_"
        }
    }
    else {
        Write-Warning "Sync script not found: $SyncScript"
    }
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Update any services that use these secrets" -ForegroundColor Gray
Write-Host "2. Restart affected deployments" -ForegroundColor Gray
Write-Host "3. Verify all services are working correctly" -ForegroundColor Gray

if ($BackupOld) {
    Write-Host "4. Remove backup file when no longer needed: $BackupFile" -ForegroundColor Gray
}

Write-Host "`n⚠️  Security Reminder:" -ForegroundColor Yellow
Write-Host "- Monitor services for any authentication failures" -ForegroundColor Yellow
Write-Host "- Update any external systems that use these secrets" -ForegroundColor Yellow
Write-Host "- Consider rotating secrets regularly (monthly/quarterly)" -ForegroundColor Yellow
