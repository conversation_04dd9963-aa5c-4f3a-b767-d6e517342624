#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Sync secrets between local and remote providers

.DESCRIPTION
    This script synchronizes secrets between local files and remote secret providers
    like GitHub Secrets, GitLab Variables, Azure Key Vault, etc.

.PARAMETER Environment
    The environment to sync secrets for (dev, test, prod, local)

.PARAMETER Provider
    Secret provider to sync with (github, gitlab, azure, aws, vault)

.PARAMETER Direction
    Sync direction: push (local to remote), pull (remote to local), or both

.PARAMETER DryRun
    Show what would be done without actually making changes

.PARAMETER Force
    Overwrite existing secrets without confirmation

.EXAMPLE
    .\sync-secrets.ps1 -Environment prod -Provider github -Direction push
    .\sync-secrets.ps1 -Environment dev -Provider gitlab -Direction pull -DryRun
    .\sync-secrets.ps1 -Environment test -Provider azure -Direction both -Force

.NOTES
    Author: MerlinHelm Team
    Version: 1.0.0
    
    Required Environment Variables:
    - GITHUB_TOKEN: For GitHub provider
    - GITLAB_TOKEN: For GitLab provider
    - AZURE_CLIENT_ID, AZURE_CLIENT_SECRET, AZURE_TENANT_ID: For Azure provider
    - AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY: For AWS provider
    - VAULT_ADDR, VAULT_TOKEN: For Vault provider
#>

param(
    [Parameter(Mandatory = $true)]
    [ValidateSet("dev", "test", "prod", "local")]
    [string]$Environment,
    
    [Parameter(Mandatory = $true)]
    [ValidateSet("github", "gitlab", "azure", "aws", "vault")]
    [string]$Provider,
    
    [Parameter(Mandatory = $false)]
    [ValidateSet("push", "pull", "both")]
    [string]$Direction = "push",
    
    [Parameter(Mandatory = $false)]
    [switch]$DryRun,
    
    [Parameter(Mandatory = $false)]
    [switch]$Force
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$SecretsDir = Split-Path -Parent $ScriptDir
$LocalSecretsDir = Join-Path $SecretsDir "local" $Environment

# Check if local secrets file exists
$LocalSecretsFile = Join-Path $LocalSecretsDir "secrets.yaml"
if (-not (Test-Path $LocalSecretsFile)) {
    Write-Error "Local secrets file not found: $LocalSecretsFile"
    Write-Host "Run generate-secrets.ps1 first to create local secrets" -ForegroundColor Yellow
    exit 1
}

# Function to read YAML file (simplified)
function Read-YamlFile {
    param([string]$FilePath)
    
    $Content = Get-Content $FilePath -Raw
    # This is a simplified YAML parser - in production, use a proper YAML library
    $Secrets = @{}
    
    $Lines = $Content -split "`n"
    $CurrentSection = $null
    $CurrentSubSection = $null
    
    foreach ($Line in $Lines) {
        $Line = $Line.Trim()
        if ($Line -match "^#" -or $Line -eq "") { continue }
        
        if ($Line -match "^(\w+):$") {
            $CurrentSection = $Matches[1]
            $Secrets[$CurrentSection] = @{}
            $CurrentSubSection = $null
        }
        elseif ($Line -match "^  (\w+):$") {
            $CurrentSubSection = $Matches[1]
            $Secrets[$CurrentSection][$CurrentSubSection] = @{}
        }
        elseif ($Line -match "^    (\w+): `"(.+)`"$") {
            $Key = $Matches[1]
            $Value = $Matches[2]
            $Secrets[$CurrentSection][$CurrentSubSection][$Key] = $Value
        }
        elseif ($Line -match "^  (\w+): `"(.+)`"$") {
            $Key = $Matches[1]
            $Value = $Matches[2]
            $Secrets[$CurrentSection][$Key] = $Value
        }
    }
    
    return $Secrets
}

# Function to sync with GitHub Secrets
function Sync-GitHubSecrets {
    param(
        [hashtable]$Secrets,
        [string]$Direction,
        [bool]$DryRun
    )
    
    $GitHubToken = $env:GITHUB_TOKEN
    if (-not $GitHubToken) {
        Write-Error "GITHUB_TOKEN environment variable is required for GitHub provider"
        exit 1
    }
    
    $GitHubRepo = $env:GITHUB_REPOSITORY
    if (-not $GitHubRepo) {
        Write-Warning "GITHUB_REPOSITORY not set, using default format"
        $GitHubRepo = "your-org/MerlinHelm"  # Default - should be configured
    }
    
    $Headers = @{
        "Authorization" = "token $GitHubToken"
        "Accept" = "application/vnd.github.v3+json"
        "User-Agent" = "MerlinHelm-Secrets-Sync"
    }
    
    $BaseUrl = "https://api.github.com/repos/$GitHubRepo"
    
    if ($Direction -eq "push" -or $Direction -eq "both") {
        Write-Host "🔄 Pushing secrets to GitHub..." -ForegroundColor Cyan
        
        # Get repository public key for encryption
        try {
            $PublicKeyResponse = Invoke-RestMethod -Uri "$BaseUrl/actions/secrets/public-key" -Headers $Headers
            $PublicKey = $PublicKeyResponse.key
            $KeyId = $PublicKeyResponse.key_id
        }
        catch {
            Write-Error "Failed to get GitHub repository public key: $_"
            exit 1
        }
        
        # Function to encrypt secret value
        function Encrypt-SecretValue {
            param([string]$Value, [string]$PublicKey)
            # This would need a proper implementation using libsodium
            # For now, return base64 encoded value (NOT SECURE - just for demo)
            return [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($Value))
        }
        
        # Flatten secrets for GitHub
        function Flatten-Secrets {
            param([hashtable]$Secrets, [string]$Prefix = "MERLINHELM_$($Environment.ToUpper())_")
            
            $FlatSecrets = @{}
            foreach ($Section in $Secrets.Keys) {
                $SectionData = $Secrets[$Section]
                if ($SectionData -is [hashtable]) {
                    foreach ($Key in $SectionData.Keys) {
                        $Value = $SectionData[$Key]
                        if ($Value -is [hashtable]) {
                            foreach ($SubKey in $Value.Keys) {
                                $SubValue = $Value[$SubKey]
                                if ($SubValue -and $SubValue -ne "") {
                                    $SecretName = "$Prefix$($Section.ToUpper())_$($Key.ToUpper())_$($SubKey.ToUpper())"
                                    $FlatSecrets[$SecretName] = $SubValue
                                }
                            }
                        }
                        else {
                            if ($Value -and $Value -ne "") {
                                $SecretName = "$Prefix$($Section.ToUpper())_$($Key.ToUpper())"
                                $FlatSecrets[$SecretName] = $Value
                            }
                        }
                    }
                }
            }
            return $FlatSecrets
        }
        
        $FlatSecrets = Flatten-Secrets -Secrets $Secrets
        
        foreach ($SecretName in $FlatSecrets.Keys) {
            $SecretValue = $FlatSecrets[$SecretName]
            
            if ($DryRun) {
                Write-Host "  [DRY RUN] Would set GitHub secret: $SecretName" -ForegroundColor Yellow
                continue
            }
            
            try {
                $EncryptedValue = Encrypt-SecretValue -Value $SecretValue -PublicKey $PublicKey
                
                $Body = @{
                    encrypted_value = $EncryptedValue
                    key_id = $KeyId
                } | ConvertTo-Json
                
                $Response = Invoke-RestMethod -Uri "$BaseUrl/actions/secrets/$SecretName" -Method PUT -Headers $Headers -Body $Body -ContentType "application/json"
                Write-Host "  ✅ Set GitHub secret: $SecretName" -ForegroundColor Green
            }
            catch {
                Write-Warning "  ❌ Failed to set GitHub secret $SecretName`: $_"
            }
        }
    }
    
    if ($Direction -eq "pull" -or $Direction -eq "both") {
        Write-Host "🔄 Pulling secrets from GitHub..." -ForegroundColor Cyan
        Write-Warning "GitHub Secrets API does not support reading secret values"
        Write-Warning "Only secret names can be retrieved for verification"
        
        try {
            $SecretsResponse = Invoke-RestMethod -Uri "$BaseUrl/actions/secrets" -Headers $Headers
            $RemoteSecrets = $SecretsResponse.secrets
            
            Write-Host "📋 Remote GitHub secrets:" -ForegroundColor Cyan
            foreach ($Secret in $RemoteSecrets) {
                Write-Host "  - $($Secret.name) (updated: $($Secret.updated_at))" -ForegroundColor Gray
            }
        }
        catch {
            Write-Error "Failed to list GitHub secrets: $_"
        }
    }
}

# Function to sync with GitLab Variables
function Sync-GitLabVariables {
    param(
        [hashtable]$Secrets,
        [string]$Direction,
        [bool]$DryRun
    )
    
    $GitLabToken = $env:GITLAB_TOKEN
    if (-not $GitLabToken) {
        Write-Error "GITLAB_TOKEN environment variable is required for GitLab provider"
        exit 1
    }
    
    $GitLabProjectId = $env:GITLAB_PROJECT_ID
    if (-not $GitLabProjectId) {
        Write-Error "GITLAB_PROJECT_ID environment variable is required for GitLab provider"
        exit 1
    }
    
    $GitLabUrl = $env:GITLAB_URL
    if (-not $GitLabUrl) {
        $GitLabUrl = "https://gitlab.com"
    }
    
    $Headers = @{
        "PRIVATE-TOKEN" = $GitLabToken
        "Content-Type" = "application/json"
    }
    
    $BaseUrl = "$GitLabUrl/api/v4/projects/$GitLabProjectId"
    
    Write-Host "🔄 Syncing with GitLab Variables..." -ForegroundColor Cyan
    Write-Host "📋 Project ID: $GitLabProjectId" -ForegroundColor Gray
    Write-Host "🌐 GitLab URL: $GitLabUrl" -ForegroundColor Gray
    
    # Implementation would go here - similar to GitHub but using GitLab API
    Write-Warning "GitLab Variables sync not yet implemented"
}

# Function to sync with Azure Key Vault
function Sync-AzureKeyVault {
    param(
        [hashtable]$Secrets,
        [string]$Direction,
        [bool]$DryRun
    )
    
    Write-Host "🔄 Syncing with Azure Key Vault..." -ForegroundColor Cyan
    Write-Warning "Azure Key Vault sync not yet implemented"
}

# Function to sync with AWS Secrets Manager
function Sync-AWSSecretsManager {
    param(
        [hashtable]$Secrets,
        [string]$Direction,
        [bool]$DryRun
    )
    
    Write-Host "🔄 Syncing with AWS Secrets Manager..." -ForegroundColor Cyan
    Write-Warning "AWS Secrets Manager sync not yet implemented"
}

# Function to sync with HashiCorp Vault
function Sync-HashiCorpVault {
    param(
        [hashtable]$Secrets,
        [string]$Direction,
        [bool]$DryRun
    )
    
    Write-Host "🔄 Syncing with HashiCorp Vault..." -ForegroundColor Cyan
    Write-Warning "HashiCorp Vault sync not yet implemented"
}

# Main execution
Write-Host "🔐 MerlinHelm Secrets Sync" -ForegroundColor Magenta
Write-Host "=========================" -ForegroundColor Magenta
Write-Host "Environment: $Environment" -ForegroundColor Cyan
Write-Host "Provider: $Provider" -ForegroundColor Cyan
Write-Host "Direction: $Direction" -ForegroundColor Cyan

if ($DryRun) {
    Write-Host "Mode: DRY RUN" -ForegroundColor Yellow
}

# Read local secrets
Write-Host "`n📖 Reading local secrets..." -ForegroundColor Cyan
$LocalSecrets = Read-YamlFile -FilePath $LocalSecretsFile
Write-Host "✅ Local secrets loaded" -ForegroundColor Green

# Sync based on provider
switch ($Provider) {
    "github" {
        Sync-GitHubSecrets -Secrets $LocalSecrets -Direction $Direction -DryRun $DryRun
    }
    "gitlab" {
        Sync-GitLabVariables -Secrets $LocalSecrets -Direction $Direction -DryRun $DryRun
    }
    "azure" {
        Sync-AzureKeyVault -Secrets $LocalSecrets -Direction $Direction -DryRun $DryRun
    }
    "aws" {
        Sync-AWSSecretsManager -Secrets $LocalSecrets -Direction $Direction -DryRun $DryRun
    }
    "vault" {
        Sync-HashiCorpVault -Secrets $LocalSecrets -Direction $Direction -DryRun $DryRun
    }
}

Write-Host "`n✅ Secrets sync completed!" -ForegroundColor Green
