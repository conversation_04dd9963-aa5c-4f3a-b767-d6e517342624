#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Generate secrets for MerlinHelm deployment

.DESCRIPTION
    This script generates random secrets for a specified environment and saves them
    to the local secrets directory. It can generate all secrets or specific ones.

.PARAMETER Environment
    The environment to generate secrets for (dev, test, prod, local)

.PARAMETER SecretType
    Specific secret type to generate (optional, generates all if not specified)

.PARAMETER Force
    Overwrite existing secrets

.PARAMETER OutputFormat
    Output format: yaml, json, env (default: yaml)

.EXAMPLE
    .\generate-secrets.ps1 -Environment dev
    .\generate-secrets.ps1 -Environment prod -SecretType openwebui -Force
    .\generate-secrets.ps1 -Environment local -OutputFormat json

.NOTES
    Author: MerlinHelm Team
    Version: 1.0.0
#>

param(
    [Parameter(Mandatory = $true)]
    [ValidateSet("dev", "test", "prod", "local")]
    [string]$Environment,
    
    [Parameter(Mandatory = $false)]
    [ValidateSet("openwebui", "ollama", "monitoring", "tls", "backup", "custom")]
    [string]$SecretType,
    
    [Parameter(Mandatory = $false)]
    [switch]$Force,
    
    [Parameter(Mandatory = $false)]
    [ValidateSet("yaml", "json", "env")]
    [string]$OutputFormat = "yaml"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$SecretsDir = Split-Path -Parent $ScriptDir
$LocalSecretsDir = Join-Path $SecretsDir "local" $Environment

# Ensure local secrets directory exists
if (-not (Test-Path $LocalSecretsDir)) {
    New-Item -ItemType Directory -Path $LocalSecretsDir -Force | Out-Null
    Write-Host "Created directory: $LocalSecretsDir" -ForegroundColor Green
}

# Function to generate random string
function New-RandomString {
    param(
        [int]$Length = 32,
        [string]$Characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    )
    
    $Random = New-Object System.Random
    $Result = ""
    for ($i = 0; $i -lt $Length; $i++) {
        $Result += $Characters[$Random.Next(0, $Characters.Length)]
    }
    return $Result
}

# Function to generate password
function New-Password {
    param([int]$Length = 16)
    $Characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*"
    return New-RandomString -Length $Length -Characters $Characters
}

# Function to generate API key
function New-ApiKey {
    param([int]$Length = 64)
    $Characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    return New-RandomString -Length $Length -Characters $Characters
}

# Function to generate JWT secret
function New-JwtSecret {
    param([int]$Length = 64)
    $Characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
    return New-RandomString -Length $Length -Characters $Characters
}

# Generate secrets structure
$Secrets = @{
    openwebui = @{
        secretKey = New-RandomString -Length 32
        jwtSecret = New-JwtSecret -Length 64
        database = @{
            username = "openwebui_user"
            password = New-Password -Length 16
            host = ""
            port = "5432"
            name = "openwebui_$Environment"
        }
        oauth = @{
            clientId = ""
            clientSecret = New-RandomString -Length 32
            issuerUrl = ""
        }
        smtp = @{
            host = ""
            port = 587
            username = ""
            password = New-Password -Length 16
            fromEmail = ""
        }
        apiKeys = @{
            openai = ""
            anthropic = ""
            google = ""
            azure = ""
        }
    }
    ollama = @{
        apiKeys = @{
            huggingface = ""
            modelRegistry = ""
        }
        registry = @{
            username = ""
            password = New-Password -Length 16
            server = ""
        }
    }
    monitoring = @{
        prometheus = @{
            username = "prometheus"
            password = New-Password -Length 16
        }
        grafana = @{
            adminPassword = New-Password -Length 16
            secretKey = New-RandomString -Length 32
        }
        external = @{
            datadogApiKey = ""
            newRelicLicenseKey = ""
            splunkToken = ""
        }
    }
    tls = @{
        cert = ""
        key = ""
        ca = ""
        keyPassword = New-Password -Length 16
        keystorePassword = New-Password -Length 16
    }
    backup = @{
        s3 = @{
            accessKey = ""
            secretKey = New-RandomString -Length 40
            bucket = "merlinhelm-backup-$Environment"
            region = "us-west-2"
            endpoint = ""
        }
        azure = @{
            accountName = ""
            accountKey = New-RandomString -Length 88
            containerName = "merlinhelm-backup-$Environment"
        }
        gcs = @{
            serviceAccountKey = ""
            bucket = "merlinhelm-backup-$Environment"
        }
    }
    custom = @{
        # Add custom secrets here
    }
}

# Filter secrets if specific type requested
if ($SecretType) {
    $FilteredSecrets = @{}
    $FilteredSecrets[$SecretType] = $Secrets[$SecretType]
    $Secrets = $FilteredSecrets
}

# Output file path
$OutputFile = Join-Path $LocalSecretsDir "secrets.$OutputFormat"

# Check if file exists and Force not specified
if ((Test-Path $OutputFile) -and -not $Force) {
    Write-Warning "Secrets file already exists: $OutputFile"
    Write-Warning "Use -Force to overwrite existing secrets"
    exit 1
}

# Generate output based on format
switch ($OutputFormat) {
    "yaml" {
        $YamlContent = "# Generated secrets for $Environment environment`n"
        $YamlContent += "# Generated on: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')`n"
        $YamlContent += "# WARNING: This file contains sensitive information. Do not commit to version control.`n`n"
        
        function ConvertTo-Yaml {
            param($Object, $Indent = 0)
            $IndentStr = "  " * $Indent
            $Result = ""
            
            foreach ($Key in $Object.Keys) {
                $Value = $Object[$Key]
                if ($Value -is [hashtable]) {
                    $Result += "$IndentStr$Key:`n"
                    $Result += ConvertTo-Yaml -Object $Value -Indent ($Indent + 1)
                } else {
                    $QuotedValue = if ($Value -eq "") { '""' } else { "`"$Value`"" }
                    $Result += "$IndentStr$Key`: $QuotedValue`n"
                }
            }
            return $Result
        }
        
        $YamlContent += ConvertTo-Yaml -Object $Secrets
        $YamlContent | Out-File -FilePath $OutputFile -Encoding UTF8
    }
    
    "json" {
        $JsonObject = @{
            metadata = @{
                environment = $Environment
                generated = Get-Date -Format 'yyyy-MM-ddTHH:mm:ssZ'
                warning = "This file contains sensitive information. Do not commit to version control."
            }
            secrets = $Secrets
        }
        $JsonObject | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputFile -Encoding UTF8
    }
    
    "env" {
        $EnvContent = "# Generated secrets for $Environment environment`n"
        $EnvContent += "# Generated on: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')`n"
        $EnvContent += "# WARNING: This file contains sensitive information. Do not commit to version control.`n`n"
        
        function ConvertTo-EnvVars {
            param($Object, $Prefix = "MERLINHELM_$($Environment.ToUpper())_")
            $Result = ""
            
            foreach ($Key in $Object.Keys) {
                $Value = $Object[$Key]
                $EnvKey = "$Prefix$($Key.ToUpper())"
                
                if ($Value -is [hashtable]) {
                    $Result += ConvertTo-EnvVars -Object $Value -Prefix "$EnvKey`_"
                } else {
                    $Result += "$EnvKey=$Value`n"
                }
            }
            return $Result
        }
        
        $EnvContent += ConvertTo-EnvVars -Object $Secrets
        $EnvContent | Out-File -FilePath $OutputFile -Encoding UTF8
    }
}

Write-Host "✅ Secrets generated successfully!" -ForegroundColor Green
Write-Host "📁 Output file: $OutputFile" -ForegroundColor Cyan
Write-Host "🔒 Environment: $Environment" -ForegroundColor Cyan

if ($SecretType) {
    Write-Host "🎯 Secret type: $SecretType" -ForegroundColor Cyan
}

Write-Host "`n⚠️  SECURITY WARNING:" -ForegroundColor Yellow
Write-Host "   - Keep this file secure and do not commit to version control" -ForegroundColor Yellow
Write-Host "   - Rotate secrets regularly" -ForegroundColor Yellow
Write-Host "   - Use different secrets for each environment" -ForegroundColor Yellow
