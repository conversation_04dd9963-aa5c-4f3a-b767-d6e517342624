# External Secrets Configuration Template
# This file configures External Secrets Operator (ESO) to fetch secrets from external providers

apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: merlinhelm-secret-store
  namespace: merlinhelm-{{ .Values.global.environment }}
spec:
  provider:
    # GitHub Secrets Provider
    github:
      auth:
        token:
          secretRef:
            name: github-token
            key: token
      owner: "your-github-org"
      repo: "your-repo-name"
    
    # Alternative: GitLab Variables Provider
    # gitlab:
    #   auth:
    #     SecretRef:
    #       secretAccessToken:
    #         name: gitlab-token
    #         key: token
    #   projectID: "12345"
    #   url: "https://gitlab.com"
    
    # Alternative: Azure Key Vault Provider
    # azurekv:
    #   authType: ServicePrincipal
    #   vaultUrl: "https://your-vault.vault.azure.net/"
    #   tenantId: "your-tenant-id"
    #   clientId: "your-client-id"
    #   clientSecret:
    #     secretRef:
    #       name: azure-secret
    #       key: client-secret
    
    # Alternative: AWS Secrets Manager Provider
    # aws:
    #   service: SecretsManager
    #   region: us-west-2
    #   auth:
    #     secretRef:
    #       accessKeyIDSecretRef:
    #         name: aws-secret
    #         key: access-key-id
    #       secretAccessKeySecretRef:
    #         name: aws-secret
    #         key: secret-access-key

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: merlinhelm-openwebui-secrets
  namespace: merlinhelm-{{ .Values.global.environment }}
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: merlinhelm-secret-store
    kind: SecretStore
  target:
    name: openwebui-external-secrets
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        secret-key: "{{ `{{ .secretKey }}` }}"
        jwt-secret: "{{ `{{ .jwtSecret }}` }}"
        database-password: "{{ `{{ .databasePassword }}` }}"
        oauth-client-secret: "{{ `{{ .oauthClientSecret }}` }}"
        smtp-password: "{{ `{{ .smtpPassword }}` }}"
        openai-api-key: "{{ `{{ .openaiApiKey }}` }}"
  data:
  - secretKey: "MERLINHELM_{{ upper .Values.global.environment }}_OPENWEBUI_SECRET_KEY"
    remoteRef:
      key: "MERLINHELM_{{ upper .Values.global.environment }}_OPENWEBUI_SECRET_KEY"
  - secretKey: "jwtSecret"
    remoteRef:
      key: "MERLINHELM_{{ upper .Values.global.environment }}_OPENWEBUI_JWT_SECRET"
  - secretKey: "databasePassword"
    remoteRef:
      key: "MERLINHELM_{{ upper .Values.global.environment }}_DATABASE_PASSWORD"
  - secretKey: "oauthClientSecret"
    remoteRef:
      key: "MERLINHELM_{{ upper .Values.global.environment }}_OAUTH_CLIENT_SECRET"
  - secretKey: "smtpPassword"
    remoteRef:
      key: "MERLINHELM_{{ upper .Values.global.environment }}_SMTP_PASSWORD"
  - secretKey: "openaiApiKey"
    remoteRef:
      key: "MERLINHELM_{{ upper .Values.global.environment }}_OPENAI_API_KEY"

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: merlinhelm-ollama-secrets
  namespace: merlinhelm-{{ .Values.global.environment }}
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: merlinhelm-secret-store
    kind: SecretStore
  target:
    name: ollama-external-secrets
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        huggingface-token: "{{ `{{ .huggingfaceToken }}` }}"
        registry-password: "{{ `{{ .registryPassword }}` }}"
  data:
  - secretKey: "huggingfaceToken"
    remoteRef:
      key: "MERLINHELM_{{ upper .Values.global.environment }}_HUGGINGFACE_TOKEN"
  - secretKey: "registryPassword"
    remoteRef:
      key: "MERLINHELM_{{ upper .Values.global.environment }}_REGISTRY_PASSWORD"

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: merlinhelm-tls-secrets
  namespace: merlinhelm-{{ .Values.global.environment }}
spec:
  refreshInterval: 24h
  secretStoreRef:
    name: merlinhelm-secret-store
    kind: SecretStore
  target:
    name: merlinhelm-tls
    creationPolicy: Owner
    template:
      type: kubernetes.io/tls
      data:
        tls.crt: "{{ `{{ .tlsCert | b64dec }}` }}"
        tls.key: "{{ `{{ .tlsKey | b64dec }}` }}"
  data:
  - secretKey: "tlsCert"
    remoteRef:
      key: "MERLINHELM_{{ upper .Values.global.environment }}_TLS_CERT"
  - secretKey: "tlsKey"
    remoteRef:
      key: "MERLINHELM_{{ upper .Values.global.environment }}_TLS_KEY"
