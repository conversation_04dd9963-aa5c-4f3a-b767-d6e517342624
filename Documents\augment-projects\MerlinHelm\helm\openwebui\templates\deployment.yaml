apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "openwebui.fullname" . }}
  labels:
    {{- include "openwebui.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "openwebui.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "openwebui.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "openwebui.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          {{- if .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: OLLAMA_BASE_URL
              value: {{ .Values.config.ollamaBaseUrl | quote }}
            - name: WEBUI_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.secretName" . }}
                  key: secret-key
            - name: WEBUI_JWT_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.secretName" . }}
                  key: jwt-secret
                  optional: true
            - name: ENABLE_SIGNUP
              value: {{ .Values.config.enableSignup | quote }}
            - name: DEFAULT_USER_ROLE
              value: {{ .Values.config.defaultUserRole | quote }}
            {{- if or .Values.secret.manual.databasePassword .Values.secret.external.enabled }}
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.secretName" . }}
                  key: database-password
                  optional: true
            {{- end }}
            {{- if or .Values.secret.manual.oauthClientSecret .Values.secret.external.enabled }}
            - name: OAUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.secretName" . }}
                  key: oauth-client-secret
                  optional: true
            {{- end }}
            {{- if or .Values.secret.manual.smtpPassword .Values.secret.external.enabled }}
            - name: SMTP_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.secretName" . }}
                  key: smtp-password
                  optional: true
            {{- end }}
            {{- if or .Values.secret.manual.openaiApiKey .Values.secret.external.enabled }}
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.secretName" . }}
                  key: openai-api-key
                  optional: true
            {{- end }}
            {{- if or .Values.secret.manual.anthropicApiKey .Values.secret.external.enabled }}
            - name: ANTHROPIC_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.secretName" . }}
                  key: anthropic-api-key
                  optional: true
            {{- end }}
            {{- if or .Values.secret.manual.googleApiKey .Values.secret.external.enabled }}
            - name: GOOGLE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.secretName" . }}
                  key: google-api-key
                  optional: true
            {{- end }}
            {{- if or .Values.secret.manual.azureApiKey .Values.secret.external.enabled }}
            - name: AZURE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "openwebui.secretName" . }}
                  key: azure-api-key
                  optional: true
            {{- end }}
            {{- if .Values.env }}
            {{- range .Values.env }}
            {{- if ne .name "WEBUI_SECRET_KEY" }}
            {{- if ne .name "OLLAMA_BASE_URL" }}
            {{- if ne .name "ENABLE_SIGNUP" }}
            {{- if ne .name "DEFAULT_USER_ROLE" }}
            - {{- toYaml . | nindent 14 }}
            {{- end }}
            {{- end }}
            {{- end }}
            {{- end }}
            {{- end }}
            {{- end }}
          {{- if .Values.persistence.enabled }}
          volumeMounts:
            - name: openwebui-data
              mountPath: {{ .Values.persistence.mountPath }}
          {{- end }}
      {{- if .Values.persistence.enabled }}
      volumes:
        - name: openwebui-data
          persistentVolumeClaim:
            claimName: {{ include "openwebui.fullname" . }}-data
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
