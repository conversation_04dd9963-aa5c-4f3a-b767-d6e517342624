# Local environment values
# Optimized for local development without GPU requirements

global:
  environment: local
  domain: local.merlinhelm.local
  storageClass: "standard"

ollama:
  enabled: true
  replicaCount: 1

  image:
    tag: "latest"
    pullPolicy: Always

  resources:
    limits:
      memory: 4Gi
      cpu: 2000m
    requests:
      memory: 2Gi
      cpu: 1000m

  persistence:
    enabled: true
    size: 20Gi
    storageClass: "standard"

  nodeSelector:
    kubernetes.io/os: linux

  tolerations: []

  service:
    type: NodePort
    port: 11434
    nodePort: 31434

  models:
    preload:
      - llama2:7b

  autoscaling:
    enabled: false

  # CPU-only configuration
  gpu:
    enabled: false

  runtimeClassName: ""

openwebui:
  enabled: true
  replicaCount: 1

  image:
    tag: "main"
    pullPolicy: Always

  # Fix security context for local development
  securityContext:
    capabilities:
      drop:
      - ALL
    readOnlyRootFilesystem: false
    runAsNonRoot: false
    runAsUser: 0

  podSecurityContext:
    fsGroup: 0

  resources:
    limits:
      memory: 2Gi
      cpu: 1000m
    requests:
      memory: 512Mi
      cpu: 250m

  persistence:
    enabled: true
    size: 5Gi
    storageClass: "standard"

  service:
    type: NodePort
    port: 8080
    nodePort: 31080

  ingress:
    enabled: true
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /
      nginx.ingress.kubernetes.io/ssl-redirect: "false"
    hosts:
      - host: openwebui.local.merlinhelm.local
        paths:
          - path: /
            pathType: Prefix
    tls: []

  config:
    ollamaBaseUrl: "http://merlinhelm-local-ollama:11434"
    enableSignup: true
    defaultUserRole: "user"
    webUIName: "MerlinHelm Local WebUI"
    enableImageGeneration: false
    enableCommunitySharing: false

  # Environment variables for local development
  env:
    - name: RAG_EMBEDDING_ENGINE
      value: ""
    - name: RAG_EMBEDDING_MODEL
      value: ""
    - name: ENABLE_RAG_HYBRID_SEARCH
      value: "false"

  autoscaling:
    enabled: false

monitoring:
  enabled: false

networkPolicies:
  enabled: false

podDisruptionBudget:
  enabled: false
