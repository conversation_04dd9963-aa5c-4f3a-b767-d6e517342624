# Default values for openwebui
# This is a YAML-formatted file.

replicaCount: 1

image:
  repository: ghcr.io/open-webui/open-webui
  pullPolicy: IfNotPresent
  tag: "main"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext:
  fsGroup: 2000

securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: false
  runAsNonRoot: true
  runAsUser: 1000

service:
  type: ClusterIP
  port: 8080
  targetPort: 8080

ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: openwebui.local
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: openwebui-tls
      hosts:
        - openwebui.local

resources:
  limits:
    memory: 2Gi
    cpu: 1000m
  requests:
    memory: 512Mi
    cpu: 250m

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 10Gi
  mountPath: /app/backend/data

# OpenWebUI Configuration
config:
  # Ollama API endpoint (will be overridden by environment-specific values)
  ollamaBaseUrl: "http://ollama:11434"

  # Authentication settings
  enableSignup: true
  defaultUserRole: "pending"

  # WebUI settings
  webUIName: "MerlinHelm WebUI"
  webUIUrl: "https://openwebui.local"

  # File upload settings
  enableImageGeneration: false
  enableCommunitySharing: false

  # Security settings
  enableLoginForm: true
  enableWebSearch: false

env:
  - name: OLLAMA_BASE_URL
    value: "http://ollama:11434"
  - name: WEBUI_SECRET_KEY
    valueFrom:
      secretKeyRef:
        name: openwebui-secret
        key: secret-key
  - name: ENABLE_SIGNUP
    value: "true"
  - name: DEFAULT_USER_ROLE
    value: "pending"

# Secret configuration
secret:
  create: true
  secretKey: ""  # Will be auto-generated if empty

  # External secrets configuration
  external:
    enabled: false  # Set to true to use External Secrets Operator
    secretStore: "merlinhelm-secret-store"  # Name of the SecretStore
    refreshInterval: "1h"  # How often to refresh secrets

    # Secret mappings for external secrets
    secrets:
      openwebui:
        secretName: "openwebui-external-secrets"
        keys:
          secretKey: "MERLINHELM_{{ .Values.global.environment | upper }}_OPENWEBUI_SECRET_KEY"
          jwtSecret: "MERLINHELM_{{ .Values.global.environment | upper }}_OPENWEBUI_JWT_SECRET"
          databasePassword: "MERLINHELM_{{ .Values.global.environment | upper }}_DATABASE_PASSWORD"
          oauthClientSecret: "MERLINHELM_{{ .Values.global.environment | upper }}_OAUTH_CLIENT_SECRET"
          smtpPassword: "MERLINHELM_{{ .Values.global.environment | upper }}_SMTP_PASSWORD"
          openaiApiKey: "MERLINHELM_{{ .Values.global.environment | upper }}_OPENAI_API_KEY"
          anthropicApiKey: "MERLINHELM_{{ .Values.global.environment | upper }}_ANTHROPIC_API_KEY"
          googleApiKey: "MERLINHELM_{{ .Values.global.environment | upper }}_GOOGLE_API_KEY"
          azureApiKey: "MERLINHELM_{{ .Values.global.environment | upper }}_AZURE_API_KEY"

  # Local secrets configuration (for development)
  local:
    enabled: false  # Set to true to use local secrets from files
    secretsPath: "/etc/secrets"  # Path where secrets are mounted

  # Manual secret values (not recommended for production)
  manual:
    secretKey: ""
    jwtSecret: ""
    databasePassword: ""
    oauthClientSecret: ""
    smtpPassword: ""
    openaiApiKey: ""
    anthropicApiKey: ""
    googleApiKey: ""
    azureApiKey: ""

livenessProbe:
  httpGet:
    path: /health
    port: http
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /health
    port: http
  initialDelaySeconds: 5
  periodSeconds: 5
