# MerlinHelm Deployment Script (PowerShell)
# Usage: .\deploy.ps1 [environment] [options]

param(
    [Parameter(Mandatory=$true, Position=0)]
    [ValidateSet("local", "dev", "test", "prod")]
    [string]$Environment,

    [string]$Namespace = "",
    [switch]$DryRun,
    [switch]$Upgrade,
    [switch]$Force,
    [switch]$UseSecrets,
    [switch]$GenerateSecrets,
    [switch]$Help
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Show-Usage {
    @"
MerlinHelm Deployment Script (PowerShell)

Usage: .\deploy.ps1 [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
    local       Deploy to local environment (CPU-only)
    dev         Deploy to development environment
    test        Deploy to test environment
    prod        Deploy to production environment

OPTIONS:
    -Namespace NAMESPACE    Kubernetes namespace (default: merlinhelm-ENV)
    -DryRun                 Perform a dry run
    -Upgrade                Upgrade existing deployment
    -Force                  Force deployment (delete and recreate)
    -UseSecrets             Enable secrets management
    -GenerateSecrets        Generate local secrets before deployment
    -Help                   Show this help message

EXAMPLES:
    .\deploy.ps1 local                    Deploy to local environment (CPU-only)
    .\deploy.ps1 dev                      Deploy to development
    .\deploy.ps1 dev -GenerateSecrets     Generate secrets and deploy to development
    .\deploy.ps1 prod -Upgrade            Upgrade production deployment
    .\deploy.ps1 prod -UseSecrets         Deploy to production with external secrets
    .\deploy.ps1 test -DryRun             Test deployment without applying changes

"@
}

function Test-Prerequisites {
    Write-Status "Checking prerequisites..."

    # Check if kubectl is installed
    try {
        kubectl version --client | Out-Null
        Write-Success "kubectl found"
    }
    catch {
        Write-Error "kubectl is not installed or not in PATH"
        exit 1
    }

    # Check if helm is installed
    try {
        helm version --short | Out-Null
        Write-Success "helm found"
    }
    catch {
        Write-Error "helm is not installed or not in PATH"
        exit 1
    }

    # Check if we can connect to Kubernetes cluster
    try {
        kubectl cluster-info | Out-Null
        Write-Success "Kubernetes cluster connection verified"
    }
    catch {
        Write-Error "Cannot connect to Kubernetes cluster"
        exit 1
    }

    # Check if chart directory exists
    $ChartPath = ".\helm\merlin-stack"
    if (-not (Test-Path $ChartPath)) {
        Write-Error "Chart directory not found: $ChartPath"
        exit 1
    }

    Write-Success "Prerequisites check passed"
}

function Setup-Namespace {
    if ([string]::IsNullOrEmpty($Namespace)) {
        $script:Namespace = "merlinhelm-$Environment"
    }

    Write-Status "Setting up namespace: $Namespace"

    try {
        kubectl get namespace $Namespace | Out-Null
        Write-Status "Namespace already exists: $Namespace"
    }
    catch {
        kubectl create namespace $Namespace
        Write-Success "Created namespace: $Namespace"
    }
}

function Setup-Secrets {
    if (-not ($UseSecrets -or $GenerateSecrets)) {
        return
    }

    Write-Status "Setting up secrets management..."

    # Check if secrets directory exists
    $SecretsDir = ".\secrets"
    if (-not (Test-Path $SecretsDir)) {
        Write-Error "Secrets directory not found: $SecretsDir"
        Write-Status "Please run the secrets setup first"
        exit 1
    }

    # Generate secrets if requested
    if ($GenerateSecrets) {
        Write-Status "Generating local secrets for environment: $Environment"
        $GenerateScript = ".\secrets\scripts\generate-secrets.ps1"

        if (Test-Path $GenerateScript) {
            try {
                & $GenerateScript -Environment $Environment
                Write-Success "Secrets generated successfully"
            }
            catch {
                Write-Error "Failed to generate secrets: $_"
                exit 1
            }
        }
        else {
            Write-Error "Generate secrets script not found: $GenerateScript"
            exit 1
        }
    }

    # Check if local secrets exist for non-production environments
    if ($Environment -ne "prod") {
        $LocalSecretsFile = ".\secrets\local\$Environment\secrets.yaml"
        if (-not (Test-Path $LocalSecretsFile)) {
            Write-Warning "Local secrets file not found: $LocalSecretsFile"
            Write-Status "Consider running with -GenerateSecrets flag"
        }
        else {
            Write-Success "Local secrets file found: $LocalSecretsFile"
        }
    }

    # For production, check external secrets configuration
    if ($Environment -eq "prod" -and $UseSecrets) {
        Write-Status "Checking external secrets configuration for production..."

        # Check if External Secrets Operator is installed
        try {
            kubectl get crd externalsecrets.external-secrets.io | Out-Null
            Write-Success "External Secrets Operator is installed"
        }
        catch {
            Write-Warning "External Secrets Operator not found"
            Write-Status "Please install External Secrets Operator for production secrets"
        }

        # Apply external secrets configuration
        $ExternalSecretsFile = ".\secrets\external\$Environment\external-secrets.yaml"
        if (Test-Path $ExternalSecretsFile) {
            Write-Status "Applying external secrets configuration..."
            try {
                kubectl apply -f $ExternalSecretsFile -n $Namespace
                Write-Success "External secrets configuration applied"
            }
            catch {
                Write-Warning "Failed to apply external secrets configuration: $_"
            }
        }
        else {
            Write-Warning "External secrets configuration not found: $ExternalSecretsFile"
        }
    }
}

function Deploy-Helm {
    $HelmArgs = @()

    # Set values file
    $ValuesPath = ".\environments\$Environment\values.yaml"

    if (-not (Test-Path $ValuesPath)) {
        Write-Error "Values file not found: $ValuesPath"
        exit 1
    }

    $HelmArgs += "--values", $ValuesPath
    $HelmArgs += "--namespace", $Namespace

    if ($DryRun) {
        $HelmArgs += "--dry-run"
        Write-Status "Performing dry run..."
    }

    $ReleaseName = "merlinhelm-$Environment"
    $ChartPath = ".\helm\merlin-stack"

    # Check if release exists
    try {
        helm list -n $Namespace | Select-String $ReleaseName | Out-Null
        $ReleaseExists = $true
    }
    catch {
        $ReleaseExists = $false
    }

    if ($ReleaseExists) {
        if ($Upgrade -or $Force) {
            if ($Force) {
                Write-Warning "Force flag detected. Uninstalling existing release..."
                helm uninstall $ReleaseName -n $Namespace
                Start-Sleep -Seconds 5
                helm install $ReleaseName $ChartPath @HelmArgs
            }
            else {
                Write-Status "Upgrading existing release..."
                helm upgrade $ReleaseName $ChartPath @HelmArgs
            }
        }
        else {
            Write-Error "Release already exists. Use -Upgrade or -Force flag."
            exit 1
        }
    }
    else {
        Write-Status "Installing new release..."
        helm install $ReleaseName $ChartPath @HelmArgs
    }

    if (-not $DryRun) {
        Write-Success "Deployment completed successfully!"

        # Show deployment status
        Write-Status "Checking deployment status..."
        kubectl get pods -n $Namespace

        # Show services
        Write-Status "Services:"
        kubectl get svc -n $Namespace

        # Show ingress if available
        try {
            kubectl get ingress -n $Namespace | Out-Null
            Write-Status "Ingress:"
            kubectl get ingress -n $Namespace
        }
        catch {
            # Ingress might not exist
        }
    }
}

# Main execution
if ($Help) {
    Show-Usage
    exit 0
}

Write-Status "Starting MerlinHelm deployment..."
Write-Status "Environment: $Environment"

if ($UseSecrets) {
    Write-Status "Secrets management: Enabled"
}

if ($GenerateSecrets) {
    Write-Status "Generate secrets: Enabled"
}

Test-Prerequisites
Setup-Namespace
Setup-Secrets
Deploy-Helm

Write-Success "MerlinHelm deployment script completed!"

if ($UseSecrets -or $GenerateSecrets) {
    Write-Status ""
    Write-Status "Secrets Management Summary:"
    if ($GenerateSecrets) {
        Write-Status "- Local secrets generated for $Environment environment"
    }
    if ($UseSecrets -and $Environment -eq "prod") {
        Write-Status "- External secrets configured for production"
    }
    Write-Status "- Review secrets configuration in ./secrets/ directory"
}
