# MerlinHelm Teardown Script (PowerShell)
# This script removes everything installed by setup-cluster.ps1 and deploy.ps1

param(
    [ValidateSet("local", "dev", "test", "prod", "all")]
    [string]$Environment = "all",
    
    [switch]$RemoveInfrastructure,
    [switch]$RemoveNamespaces,
    [switch]$RemoveStorage,
    [switch]$Force,
    [switch]$DryRun,
    [switch]$Help
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Show-Usage {
    @"
MerlinHelm Teardown Script (PowerShell)

Usage: .\teardown.ps1 [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
    local               Remove local environment only
    dev                 Remove development environment only
    test                Remove test environment only
    prod                Remove production environment only
    all                 Remove all environments (default)

OPTIONS:
    -RemoveInfrastructure   Remove cluster infrastructure (ingress, cert-manager, etc.)
    -RemoveNamespaces       Remove MerlinHelm namespaces
    -RemoveStorage          Remove persistent volumes and data
    -Force                  Skip confirmation prompts
    -DryRun                 Show what would be removed without actually removing
    -Help                   Show this help message

EXAMPLES:
    .\teardown.ps1 local                    Remove only local environment
    .\teardown.ps1 all -RemoveInfrastructure Remove everything including infrastructure
    .\teardown.ps1 prod -DryRun             Show what would be removed from prod
    .\teardown.ps1 -Force                   Remove all environments without prompts

WARNING: This script will permanently delete deployments and data!

"@
}

function Confirm-Action {
    param([string]$Message)
    
    if ($Force -or $DryRun) {
        return $true
    }
    
    $response = Read-Host "$Message (y/N)"
    return $response -eq "y" -or $response -eq "Y"
}

function Remove-HelmReleases {
    param([string[]]$Environments)
    
    Write-Status "Removing Helm releases..."
    
    foreach ($env in $Environments) {
        $namespace = "merlinhelm-$env"
        $releaseName = "merlinhelm-$env"
        
        try {
            $releases = helm list -n $namespace 2>$null
            if ($releases -and $releases.Count -gt 1) {
                Write-Status "Found Helm release in namespace: $namespace"
                
                if ($DryRun) {
                    Write-Warning "[DRY RUN] Would remove Helm release: $releaseName"
                }
                else {
                    if (Confirm-Action "Remove Helm release '$releaseName' from namespace '$namespace'?") {
                        helm uninstall $releaseName -n $namespace
                        Write-Success "Removed Helm release: $releaseName"
                    }
                }
            }
            else {
                Write-Status "No Helm releases found in namespace: $namespace"
            }
        }
        catch {
            Write-Warning "Could not check Helm releases in namespace: $namespace"
        }
    }
}

function Remove-Namespaces {
    param([string[]]$Environments)
    
    Write-Status "Removing namespaces..."
    
    foreach ($env in $Environments) {
        $namespace = "merlinhelm-$env"
        
        try {
            kubectl get namespace $namespace 2>$null | Out-Null
            Write-Status "Found namespace: $namespace"
            
            if ($DryRun) {
                Write-Warning "[DRY RUN] Would remove namespace: $namespace"
            }
            else {
                if (Confirm-Action "Remove namespace '$namespace' and all its resources?") {
                    kubectl delete namespace $namespace --timeout=300s
                    Write-Success "Removed namespace: $namespace"
                }
            }
        }
        catch {
            Write-Status "Namespace not found: $namespace"
        }
    }
}

function Remove-PersistentVolumes {
    param([string[]]$Environments)
    
    if (-not $RemoveStorage) {
        Write-Status "Skipping persistent volume removal (use -RemoveStorage to remove)"
        return
    }
    
    Write-Status "Removing persistent volumes..."
    
    foreach ($env in $Environments) {
        $namespace = "merlinhelm-$env"
        
        try {
            $pvcs = kubectl get pvc -n $namespace -o name 2>$null
            if ($pvcs) {
                Write-Status "Found persistent volume claims in namespace: $namespace"
                
                if ($DryRun) {
                    Write-Warning "[DRY RUN] Would remove PVCs in namespace: $namespace"
                    kubectl get pvc -n $namespace
                }
                else {
                    if (Confirm-Action "Remove all persistent volume claims in namespace '$namespace'? This will DELETE ALL DATA!") {
                        kubectl delete pvc --all -n $namespace
                        Write-Success "Removed PVCs from namespace: $namespace"
                    }
                }
            }
        }
        catch {
            Write-Status "No PVCs found in namespace: $namespace"
        }
    }
}

function Remove-Infrastructure {
    if (-not $RemoveInfrastructure) {
        Write-Status "Skipping infrastructure removal (use -RemoveInfrastructure to remove)"
        return
    }
    
    Write-Status "Removing cluster infrastructure..."
    
    # Remove cert-manager
    try {
        kubectl get namespace cert-manager 2>$null | Out-Null
        Write-Status "Found cert-manager installation"
        
        if ($DryRun) {
            Write-Warning "[DRY RUN] Would remove cert-manager"
        }
        else {
            if (Confirm-Action "Remove cert-manager and all certificates?") {
                # Remove ClusterIssuers first
                kubectl delete clusterissuer --all 2>$null
                
                # Remove cert-manager
                helm uninstall cert-manager -n cert-manager 2>$null
                kubectl delete namespace cert-manager 2>$null
                Write-Success "Removed cert-manager"
            }
        }
    }
    catch {
        Write-Status "cert-manager not found"
    }
    
    # Remove NGINX Ingress Controller
    try {
        kubectl get namespace ingress-nginx 2>$null | Out-Null
        Write-Status "Found NGINX Ingress Controller installation"
        
        if ($DryRun) {
            Write-Warning "[DRY RUN] Would remove NGINX Ingress Controller"
        }
        else {
            if (Confirm-Action "Remove NGINX Ingress Controller?") {
                helm uninstall ingress-nginx -n ingress-nginx 2>$null
                kubectl delete namespace ingress-nginx 2>$null
                Write-Success "Removed NGINX Ingress Controller"
            }
        }
    }
    catch {
        Write-Status "NGINX Ingress Controller not found"
    }
    
    # Remove GPU support
    try {
        kubectl get daemonset nvidia-device-plugin-daemonset -n kube-system 2>$null | Out-Null
        Write-Status "Found NVIDIA Device Plugin"
        
        if ($DryRun) {
            Write-Warning "[DRY RUN] Would remove NVIDIA Device Plugin"
        }
        else {
            if (Confirm-Action "Remove NVIDIA Device Plugin and GPU support?") {
                kubectl delete -f .\kubernetes\gpu-runtime-class.yaml 2>$null
                Write-Success "Removed GPU support"
            }
        }
    }
    catch {
        Write-Status "NVIDIA Device Plugin not found"
    }
}

function Remove-NodeLabels {
    Write-Status "Removing node labels..."
    
    $nodes = kubectl get nodes -o name 2>$null
    foreach ($node in $nodes) {
        $nodeName = $node -replace "node/", ""
        
        if ($DryRun) {
            Write-Warning "[DRY RUN] Would remove labels from node: $nodeName"
        }
        else {
            # Remove MerlinHelm-specific labels
            kubectl label nodes $nodeName accelerator- 2>$null
            kubectl label nodes $nodeName node-type- 2>$null
        }
    }
    
    if (-not $DryRun) {
        Write-Success "Removed node labels"
    }
}

function Show-Summary {
    param([string[]]$Environments)
    
    Write-Status "Teardown Summary:"
    Write-Host "  Environments: $($Environments -join ', ')" -ForegroundColor $Blue
    Write-Host "  Remove Infrastructure: $RemoveInfrastructure" -ForegroundColor $Blue
    Write-Host "  Remove Namespaces: $RemoveNamespaces" -ForegroundColor $Blue
    Write-Host "  Remove Storage: $RemoveStorage" -ForegroundColor $Blue
    Write-Host "  Dry Run: $DryRun" -ForegroundColor $Blue
    Write-Host ""
}

# Main execution
if ($Help) {
    Show-Usage
    exit 0
}

# Determine environments to remove
$environmentsToRemove = @()
if ($Environment -eq "all") {
    $environmentsToRemove = @("local", "dev", "test", "prod")
}
else {
    $environmentsToRemove = @($Environment)
}

Write-Status "Starting MerlinHelm teardown..."
Show-Summary -Environments $environmentsToRemove

if (-not $Force -and -not $DryRun) {
    Write-Warning "This will permanently remove MerlinHelm deployments and potentially data!"
    if (-not (Confirm-Action "Are you sure you want to continue?")) {
        Write-Status "Teardown cancelled by user"
        exit 0
    }
}

# Execute teardown steps
Remove-HelmReleases -Environments $environmentsToRemove

if ($RemoveNamespaces) {
    Remove-Namespaces -Environments $environmentsToRemove
}

Remove-PersistentVolumes -Environments $environmentsToRemove

Remove-Infrastructure

Remove-NodeLabels

if ($DryRun) {
    Write-Success "Dry run completed - no changes were made"
}
else {
    Write-Success "MerlinHelm teardown completed!"
    Write-Status "Note: Some resources may take a few minutes to fully terminate"
}
