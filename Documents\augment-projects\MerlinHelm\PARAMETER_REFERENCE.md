# MerlinHelm Parameter Reference

This document provides a quick reference for the correct parameter syntax for both PowerShell and Bash scripts.

## 🔧 Script Parameter Syntax

### PowerShell Scripts (Windows)
- **File Extension**: `.ps1`
- **Parameter Format**: `-ParameterName` (single dash, PascalCase)
- **Example**: `.\scripts\teardown.ps1 local -RemoveStorage -DryRun`

### Bash Scripts (Linux/macOS)
- **File Extension**: `.sh`
- **Parameter Format**: `--parameter-name` (double dash, kebab-case)
- **Example**: `./scripts/teardown.sh local --remove-storage --dry-run`

## 📋 Complete Parameter Reference

### Teardown Script Parameters

| PowerShell | Bash | Description |
|------------|------|-------------|
| `-RemoveInfrastructure` | `--remove-infrastructure` | Remove cluster infrastructure (ingress, cert-manager, GPU support) |
| `-RemoveNamespaces` | `--remove-namespaces` | Remove MerlinHelm namespaces |
| `-RemoveStorage` | `--remove-storage` | Remove persistent volumes and data (⚠️ DATA LOSS) |
| `-Force` | `--force` | Skip confirmation prompts |
| `-DryRun` | `--dry-run` | Show what would be removed without making changes |
| `-Help` | `--help` | Show help message |

### Deploy Script Parameters

| PowerShell | Bash | Description |
|------------|------|-------------|
| `-Upgrade` | `--upgrade` | Upgrade existing deployment |
| `-Force` | `--force` | Force deployment (delete and recreate) |
| `-DryRun` | `--dry-run` | Show what would be deployed without making changes |
| `-Help` | `--help` | Show help message |

### Setup Script Parameters

| PowerShell | Bash | Description |
|------------|------|-------------|
| `-All` | `--all` | Install all components |
| `-IngressController` | `--ingress-controller` | Install NGINX Ingress Controller |
| `-CertManager` | `--cert-manager` | Install cert-manager |
| `-GPUSupport` | `--gpu-support` | Install GPU support |
| `-Force` | `--force` | Skip confirmation prompts |
| `-Help` | `--help` | Show help message |

## 🎯 Common Usage Examples

### Teardown Examples

#### Remove Local Environment
```powershell
# PowerShell
.\scripts\teardown.ps1 local

# Bash
./scripts/teardown.sh local
```

#### Complete Teardown with Storage
```powershell
# PowerShell
.\scripts\teardown.ps1 all -RemoveInfrastructure -RemoveStorage -RemoveNamespaces

# Bash
./scripts/teardown.sh all --remove-infrastructure --remove-storage --remove-namespaces
```

#### Dry Run
```powershell
# PowerShell
.\scripts\teardown.ps1 all -DryRun

# Bash
./scripts/teardown.sh all --dry-run
```

### Deploy Examples

#### Deploy to Local Environment
```powershell
# PowerShell
.\scripts\deploy.ps1 local

# Bash
./scripts/deploy.sh local
```

#### Force Deploy
```powershell
# PowerShell
.\scripts\deploy.ps1 dev -Force

# Bash
./scripts/deploy.sh dev --force
```

#### Upgrade Deployment
```powershell
# PowerShell
.\scripts\deploy.ps1 prod -Upgrade

# Bash
./scripts/deploy.sh prod --upgrade
```

### Setup Examples

#### Setup All Components
```powershell
# PowerShell
.\scripts\setup-cluster.ps1 -All

# Bash
./scripts/setup-cluster.sh --all
```

#### Setup Only Ingress
```powershell
# PowerShell
.\scripts\setup-cluster.ps1 -IngressController

# Bash
./scripts/setup-cluster.sh --ingress-controller
```

## ⚠️ Common Mistakes

### ❌ Wrong Parameter Format
```powershell
# WRONG - Using Bash syntax in PowerShell
.\scripts\teardown.ps1 local --remove-storage

# CORRECT - PowerShell syntax
.\scripts\teardown.ps1 local -RemoveStorage
```

```bash
# WRONG - Using PowerShell syntax in Bash
./scripts/teardown.sh local -RemoveStorage

# CORRECT - Bash syntax
./scripts/teardown.sh local --remove-storage
```

### ❌ Wrong File Extension
```powershell
# WRONG - Calling Bash script on Windows
.\scripts\teardown.sh local

# CORRECT - PowerShell script on Windows
.\scripts\teardown.ps1 local
```

```bash
# WRONG - Calling PowerShell script on Linux
./scripts/teardown.ps1 local

# CORRECT - Bash script on Linux
./scripts/teardown.sh local
```

## 🔍 How to Remember

### PowerShell (Windows)
- **Single dash**: `-`
- **PascalCase**: `RemoveStorage` (capital letters for each word)
- **File extension**: `.ps1`
- **Path separator**: `.\` (backslash)

### Bash (Linux/macOS)
- **Double dash**: `--`
- **kebab-case**: `remove-storage` (lowercase with hyphens)
- **File extension**: `.sh`
- **Path separator**: `./` (forward slash)

## 📚 Getting Help

### Show Help for Any Script
```powershell
# PowerShell
.\scripts\teardown.ps1 -Help
.\scripts\deploy.ps1 -Help
.\scripts\setup-cluster.ps1 -Help
```

```bash
# Bash
./scripts/teardown.sh --help
./scripts/deploy.sh --help
./scripts/setup-cluster.sh --help
```

## 🎯 Quick Reference Card

| Action | PowerShell (Windows) | Bash (Linux/macOS) |
|--------|---------------------|---------------------|
| **Remove local env** | `.\scripts\teardown.ps1 local` | `./scripts/teardown.sh local` |
| **Remove with storage** | `.\scripts\teardown.ps1 local -RemoveStorage` | `./scripts/teardown.sh local --remove-storage` |
| **Complete teardown** | `.\scripts\teardown.ps1 all -RemoveInfrastructure -RemoveStorage -RemoveNamespaces` | `./scripts/teardown.sh all --remove-infrastructure --remove-storage --remove-namespaces` |
| **Dry run** | `.\scripts\teardown.ps1 all -DryRun` | `./scripts/teardown.sh all --dry-run` |
| **Deploy local** | `.\scripts\deploy.ps1 local` | `./scripts/deploy.sh local` |
| **Force deploy** | `.\scripts\deploy.ps1 dev -Force` | `./scripts/deploy.sh dev --force` |
| **Setup all** | `.\scripts\setup-cluster.ps1 -All` | `./scripts/setup-cluster.sh --all` |
| **Show help** | `.\scripts\teardown.ps1 -Help` | `./scripts/teardown.sh --help` |

Remember: **PowerShell uses single dash + PascalCase**, **Bash uses double dash + kebab-case**!
