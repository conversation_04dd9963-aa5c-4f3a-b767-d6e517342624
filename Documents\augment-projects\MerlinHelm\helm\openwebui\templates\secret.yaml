{{- if and .Values.secret.create (not .Values.secret.external.enabled) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "openwebui.fullname" . }}-secret
  labels:
    {{- include "openwebui.labels" . | nindent 4 }}
  annotations:
    helm.sh/resource-policy: keep
type: Opaque
data:
  secret-key: {{ include "openwebui.secretKey" . | b64enc | quote }}
  {{- if .Values.secret.manual.jwtSecret }}
  jwt-secret: {{ .Values.secret.manual.jwtSecret | b64enc | quote }}
  {{- else }}
  jwt-secret: {{ include "openwebui.jwtSecret" . | b64enc | quote }}
  {{- end }}
  {{- if .Values.secret.manual.databasePassword }}
  database-password: {{ .Values.secret.manual.databasePassword | b64enc | quote }}
  {{- end }}
  {{- if .Values.secret.manual.oauthClientSecret }}
  oauth-client-secret: {{ .Values.secret.manual.oauthClientSecret | b64enc | quote }}
  {{- end }}
  {{- if .Values.secret.manual.smtpPassword }}
  smtp-password: {{ .Values.secret.manual.smtpPassword | b64enc | quote }}
  {{- end }}
  {{- if .Values.secret.manual.openaiApiKey }}
  openai-api-key: {{ .Values.secret.manual.openaiApiKey | b64enc | quote }}
  {{- end }}
  {{- if .Values.secret.manual.anthropicApiKey }}
  anthropic-api-key: {{ .Values.secret.manual.anthropicApiKey | b64enc | quote }}
  {{- end }}
  {{- if .Values.secret.manual.googleApiKey }}
  google-api-key: {{ .Values.secret.manual.googleApiKey | b64enc | quote }}
  {{- end }}
  {{- if .Values.secret.manual.azureApiKey }}
  azure-api-key: {{ .Values.secret.manual.azureApiKey | b64enc | quote }}
  {{- end }}
{{- end }}

{{- if .Values.secret.external.enabled }}
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: {{ include "openwebui.fullname" . }}-external-secret
  labels:
    {{- include "openwebui.labels" . | nindent 4 }}
spec:
  refreshInterval: {{ .Values.secret.external.refreshInterval }}
  secretStoreRef:
    name: {{ .Values.secret.external.secretStore }}
    kind: SecretStore
  target:
    name: {{ include "openwebui.fullname" . }}-secret
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        secret-key: "{{ `{{ .secretKey }}` }}"
        jwt-secret: "{{ `{{ .jwtSecret }}` }}"
        {{- if .Values.secret.external.secrets.openwebui.keys.databasePassword }}
        database-password: "{{ `{{ .databasePassword }}` }}"
        {{- end }}
        {{- if .Values.secret.external.secrets.openwebui.keys.oauthClientSecret }}
        oauth-client-secret: "{{ `{{ .oauthClientSecret }}` }}"
        {{- end }}
        {{- if .Values.secret.external.secrets.openwebui.keys.smtpPassword }}
        smtp-password: "{{ `{{ .smtpPassword }}` }}"
        {{- end }}
        {{- if .Values.secret.external.secrets.openwebui.keys.openaiApiKey }}
        openai-api-key: "{{ `{{ .openaiApiKey }}` }}"
        {{- end }}
        {{- if .Values.secret.external.secrets.openwebui.keys.anthropicApiKey }}
        anthropic-api-key: "{{ `{{ .anthropicApiKey }}` }}"
        {{- end }}
        {{- if .Values.secret.external.secrets.openwebui.keys.googleApiKey }}
        google-api-key: "{{ `{{ .googleApiKey }}` }}"
        {{- end }}
        {{- if .Values.secret.external.secrets.openwebui.keys.azureApiKey }}
        azure-api-key: "{{ `{{ .azureApiKey }}` }}"
        {{- end }}
  data:
  - secretKey: "secretKey"
    remoteRef:
      key: {{ .Values.secret.external.secrets.openwebui.keys.secretKey | quote }}
  - secretKey: "jwtSecret"
    remoteRef:
      key: {{ .Values.secret.external.secrets.openwebui.keys.jwtSecret | quote }}
  {{- if .Values.secret.external.secrets.openwebui.keys.databasePassword }}
  - secretKey: "databasePassword"
    remoteRef:
      key: {{ .Values.secret.external.secrets.openwebui.keys.databasePassword | quote }}
  {{- end }}
  {{- if .Values.secret.external.secrets.openwebui.keys.oauthClientSecret }}
  - secretKey: "oauthClientSecret"
    remoteRef:
      key: {{ .Values.secret.external.secrets.openwebui.keys.oauthClientSecret | quote }}
  {{- end }}
  {{- if .Values.secret.external.secrets.openwebui.keys.smtpPassword }}
  - secretKey: "smtpPassword"
    remoteRef:
      key: {{ .Values.secret.external.secrets.openwebui.keys.smtpPassword | quote }}
  {{- end }}
  {{- if .Values.secret.external.secrets.openwebui.keys.openaiApiKey }}
  - secretKey: "openaiApiKey"
    remoteRef:
      key: {{ .Values.secret.external.secrets.openwebui.keys.openaiApiKey | quote }}
  {{- end }}
  {{- if .Values.secret.external.secrets.openwebui.keys.anthropicApiKey }}
  - secretKey: "anthropicApiKey"
    remoteRef:
      key: {{ .Values.secret.external.secrets.openwebui.keys.anthropicApiKey | quote }}
  {{- end }}
  {{- if .Values.secret.external.secrets.openwebui.keys.googleApiKey }}
  - secretKey: "googleApiKey"
    remoteRef:
      key: {{ .Values.secret.external.secrets.openwebui.keys.googleApiKey | quote }}
  {{- end }}
  {{- if .Values.secret.external.secrets.openwebui.keys.azureApiKey }}
  - secretKey: "azureApiKey"
    remoteRef:
      key: {{ .Values.secret.external.secrets.openwebui.keys.azureApiKey | quote }}
  {{- end }}
{{- end }}
