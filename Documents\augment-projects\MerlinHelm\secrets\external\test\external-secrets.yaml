# External Secrets Configuration for Test Environment
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: merlinhelm-secret-store
  namespace: merlinhelm-test
spec:
  provider:
    github:
      auth:
        token:
          secretRef:
            name: github-token
            key: token
      owner: "your-github-org"  # Replace with your GitHub organization
      repo: "MerlinHelm"        # Replace with your repository name

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: merlinhelm-openwebui-secrets
  namespace: merlinhelm-test
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: merlinhelm-secret-store
    kind: SecretStore
  target:
    name: openwebui-external-secrets
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        secret-key: "{{ .secretKey }}"
        jwt-secret: "{{ .jwtSecret }}"
        database-password: "{{ .databasePassword }}"
        oauth-client-secret: "{{ .oauthClientSecret }}"
        smtp-password: "{{ .smtpPassword }}"
        openai-api-key: "{{ .openaiApiKey }}"
  data:
  - secretKey: "secretKey"
    remoteRef:
      key: "MERLINHELM_TEST_OPENWEBUI_SECRET_KEY"
  - secretKey: "jwtSecret"
    remoteRef:
      key: "MERLINHELM_TEST_OPENWEBUI_JWT_SECRET"
  - secretKey: "databasePassword"
    remoteRef:
      key: "MERLINHELM_TEST_DATABASE_PASSWORD"
  - secretKey: "oauthClientSecret"
    remoteRef:
      key: "MERLINHELM_TEST_OAUTH_CLIENT_SECRET"
  - secretKey: "smtpPassword"
    remoteRef:
      key: "MERLINHELM_TEST_SMTP_PASSWORD"
  - secretKey: "openaiApiKey"
    remoteRef:
      key: "MERLINHELM_TEST_OPENAI_API_KEY"

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: merlinhelm-ollama-secrets
  namespace: merlinhelm-test
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: merlinhelm-secret-store
    kind: SecretStore
  target:
    name: ollama-external-secrets
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        huggingface-token: "{{ .huggingfaceToken }}"
        registry-password: "{{ .registryPassword }}"
  data:
  - secretKey: "huggingfaceToken"
    remoteRef:
      key: "MERLINHELM_TEST_HUGGINGFACE_TOKEN"
  - secretKey: "registryPassword"
    remoteRef:
      key: "MERLINHELM_TEST_REGISTRY_PASSWORD"

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: merlinhelm-tls-secrets
  namespace: merlinhelm-test
spec:
  refreshInterval: 24h
  secretStoreRef:
    name: merlinhelm-secret-store
    kind: SecretStore
  target:
    name: merlinhelm-tls
    creationPolicy: Owner
    template:
      type: kubernetes.io/tls
      data:
        tls.crt: "{{ .tlsCert | b64dec }}"
        tls.key: "{{ .tlsKey | b64dec }}"
  data:
  - secretKey: "tlsCert"
    remoteRef:
      key: "MERLINHELM_TEST_TLS_CERT"
  - secretKey: "tlsKey"
    remoteRef:
      key: "MERLINHELM_TEST_TLS_KEY"
