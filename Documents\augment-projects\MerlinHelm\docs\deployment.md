# MerlinHelm Deployment Guide

This guide provides detailed instructions for deploying MerlinHelm (Ollama + OpenWebUI) to Kubernetes clusters.

## Prerequisites

### System Requirements

- **Kubernetes cluster** (version 1.24+)
- **Helm** (version 3.x)
- **kubectl** configured to access your cluster
- **GPU nodes** with NVIDIA drivers installed
- **NVIDIA Container Toolkit** on GPU nodes
- **Ingress controller** (NGINX recommended)

### GPU Requirements

- NVIDIA GPU with CUDA support
- Minimum 8GB GPU memory for Ollama
- NVIDIA drivers version 470.57.02+
- NVIDIA Container Toolkit

## Quick Start

### 1. Clone Repository

```bash
git clone <repository-url>
cd MerlinHelm
```

### 2. Setup GPU Nodes

```bash
# Run on each GPU node
chmod +x scripts/setup-gpu-nodes.sh
./scripts/setup-gpu-nodes.sh --all
```

### 3. Deploy to Development

```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh dev
```

## Detailed Deployment Steps

### Step 1: Prepare Your Cluster

#### Install NGINX Ingress Controller

```bash
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update
helm install ingress-nginx ingress-nginx/ingress-nginx \
  --namespace ingress-nginx \
  --create-namespace
```

#### Install cert-manager (for TLS)

```bash
helm repo add jetstack https://charts.jetstack.io
helm repo update
helm install cert-manager jetstack/cert-manager \
  --namespace cert-manager \
  --create-namespace \
  --set installCRDs=true
```

### Step 2: Configure GPU Support

#### Label GPU Nodes

```bash
# Replace NODE_NAME with your GPU node name
kubectl label nodes NODE_NAME accelerator=nvidia-tesla-k80
kubectl label nodes NODE_NAME node-type=gpu
```

#### Apply GPU RuntimeClass

```bash
kubectl apply -f kubernetes/gpu-runtime-class.yaml
```

### Step 3: Deploy MerlinHelm

#### Development Environment

```bash
./scripts/deploy.sh dev
```

#### Test Environment

```bash
./scripts/deploy.sh test
```

#### Production Environment

```bash
./scripts/deploy.sh prod
```

## Environment-Specific Configurations

### Development (dev)

- **Purpose**: Local development and testing
- **Resources**: Minimal (2-4GB RAM, 1 GPU)
- **Replicas**: 1 for each service
- **Storage**: Local storage
- **Ingress**: HTTP only
- **Models**: Basic models (llama2:7b)

### Test (test)

- **Purpose**: Staging and integration testing
- **Resources**: Moderate (4-8GB RAM, 1-2 GPUs)
- **Replicas**: 2 for each service
- **Storage**: Persistent volumes
- **Ingress**: HTTPS with staging certificates
- **Models**: Multiple models for testing

### Production (prod)

- **Purpose**: Production workloads
- **Resources**: High (8-16GB RAM, 2+ GPUs)
- **Replicas**: 3+ with auto-scaling
- **Storage**: High-performance persistent volumes
- **Ingress**: HTTPS with production certificates
- **Models**: Full model suite

## Custom Deployment

### Using Helm Directly

```bash
# Install Ollama only
helm install ollama ./helm/ollama \
  --namespace merlinhelm \
  --create-namespace \
  --values environments/dev/values.yaml

# Install OpenWebUI only
helm install openwebui ./helm/openwebui \
  --namespace merlinhelm \
  --values environments/dev/values.yaml

# Install complete stack
helm install merlinhelm ./helm/merlin-stack \
  --namespace merlinhelm \
  --create-namespace \
  --values environments/dev/values.yaml
```

### Using kubectl with Raw Manifests

```bash
# Create namespaces
kubectl apply -f kubernetes/namespace.yaml

# Apply GPU support
kubectl apply -f kubernetes/gpu-runtime-class.yaml

# Generate and apply manifests
helm template merlinhelm ./helm/merlin-stack \
  --values environments/prod/values.yaml \
  --namespace merlinhelm-prod > manifests.yaml
kubectl apply -f manifests.yaml
```

## Verification

### Check Pod Status

```bash
kubectl get pods -n merlinhelm-dev
kubectl get pods -n merlinhelm-test
kubectl get pods -n merlinhelm-prod
```

### Check Services

```bash
kubectl get svc -n merlinhelm-dev
```

### Check Ingress

```bash
kubectl get ingress -n merlinhelm-dev
```

### Test Ollama API

```bash
# Port forward to test locally
kubectl port-forward svc/ollama 11434:11434 -n merlinhelm-dev

# Test API
curl http://localhost:11434/api/tags
```

### Test OpenWebUI

```bash
# Port forward to test locally
kubectl port-forward svc/openwebui 8080:8080 -n merlinhelm-dev

# Open browser to http://localhost:8080
```

## Troubleshooting

### Common Issues

#### GPU Not Detected

```bash
# Check GPU nodes
kubectl describe nodes | grep nvidia.com/gpu

# Check NVIDIA device plugin
kubectl get pods -n kube-system | grep nvidia
```

#### Pods Stuck in Pending

```bash
# Check events
kubectl describe pod POD_NAME -n NAMESPACE

# Check resource availability
kubectl top nodes
```

#### Ingress Not Working

```bash
# Check ingress controller
kubectl get pods -n ingress-nginx

# Check ingress configuration
kubectl describe ingress -n NAMESPACE
```

## Scaling

### Manual Scaling

```bash
# Scale Ollama
kubectl scale deployment ollama --replicas=3 -n merlinhelm-prod

# Scale OpenWebUI
kubectl scale deployment openwebui --replicas=5 -n merlinhelm-prod
```

### Auto-scaling

Auto-scaling is configured in production environment values. Adjust HPA settings:

```yaml
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 60
  targetMemoryUtilizationPercentage: 70
```

## Monitoring

### Check Resource Usage

```bash
kubectl top pods -n merlinhelm-prod
kubectl top nodes
```

### View Logs

```bash
# Ollama logs
kubectl logs -f deployment/ollama -n merlinhelm-prod

# OpenWebUI logs
kubectl logs -f deployment/openwebui -n merlinhelm-prod
```

## Backup and Recovery

### Backup Persistent Volumes

```bash
# Create volume snapshots (if supported)
kubectl get volumesnapshots -n merlinhelm-prod
```

### Export Configuration

```bash
# Export current configuration
helm get values merlinhelm -n merlinhelm-prod > backup-values.yaml
```

## Security Considerations

- Use network policies to restrict traffic
- Enable RBAC
- Use secrets for sensitive data
- Regular security updates
- Monitor access logs

## Next Steps

- [Configuration Reference](configuration.md)
- [Troubleshooting Guide](troubleshooting.md)
- [Architecture Overview](architecture.md)
