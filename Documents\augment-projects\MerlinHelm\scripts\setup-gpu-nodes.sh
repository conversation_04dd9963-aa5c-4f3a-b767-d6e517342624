#!/bin/bash

# GPU Node Setup Script for MerlinHelm
# This script sets up GPU nodes for Ollama deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running on GPU node
check_gpu_node() {
    print_status "Checking for GPU hardware..."
    
    if command -v nvidia-smi &> /dev/null; then
        nvidia-smi
        print_success "NVIDIA GPU detected"
    else
        print_warning "nvidia-smi not found. This might not be a GPU node."
    fi
}

# Function to install NVIDIA Container Toolkit
install_nvidia_container_toolkit() {
    print_status "Installing NVIDIA Container Toolkit..."
    
    # Add NVIDIA package repository
    distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
    curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
    curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
    
    # Update package list
    sudo apt-get update
    
    # Install nvidia-container-toolkit
    sudo apt-get install -y nvidia-container-toolkit
    
    # Restart Docker
    sudo systemctl restart docker
    
    print_success "NVIDIA Container Toolkit installed"
}

# Function to install NVIDIA Device Plugin for Kubernetes
install_nvidia_device_plugin() {
    print_status "Installing NVIDIA Device Plugin for Kubernetes..."
    
    kubectl apply -f https://raw.githubusercontent.com/NVIDIA/k8s-device-plugin/v0.14.1/nvidia-device-plugin.yml
    
    print_success "NVIDIA Device Plugin installed"
}

# Function to label GPU nodes
label_gpu_nodes() {
    print_status "Labeling GPU nodes..."
    
    # Get current node name
    NODE_NAME=$(kubectl get nodes -o jsonpath='{.items[0].metadata.name}')
    
    # Label the node as GPU node
    kubectl label nodes $NODE_NAME accelerator=nvidia-tesla-k80 --overwrite
    kubectl label nodes $NODE_NAME node-type=gpu --overwrite
    
    print_success "GPU node labeled: $NODE_NAME"
}

# Function to create GPU runtime class
create_gpu_runtime_class() {
    print_status "Creating GPU RuntimeClass..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: node.k8s.io/v1
kind: RuntimeClass
metadata:
  name: nvidia
handler: nvidia
EOF
    
    print_success "GPU RuntimeClass created"
}

# Function to test GPU setup
test_gpu_setup() {
    print_status "Testing GPU setup with sample pod..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: gpu-test
spec:
  restartPolicy: Never
  runtimeClassName: nvidia
  containers:
  - name: gpu-test
    image: nvidia/cuda:11.0.3-base-ubuntu20.04
    command: ["nvidia-smi"]
    resources:
      limits:
        nvidia.com/gpu: 1
EOF
    
    # Wait for pod to complete
    kubectl wait --for=condition=Ready pod/gpu-test --timeout=60s
    
    # Show logs
    kubectl logs gpu-test
    
    # Cleanup
    kubectl delete pod gpu-test
    
    print_success "GPU test completed"
}

# Function to show usage
show_usage() {
    cat << EOF
GPU Node Setup Script for MerlinHelm

Usage: $0 [OPTIONS]

OPTIONS:
    --install-toolkit       Install NVIDIA Container Toolkit
    --install-device-plugin Install NVIDIA Device Plugin for Kubernetes
    --label-nodes          Label GPU nodes
    --create-runtime-class Create GPU RuntimeClass
    --test                 Test GPU setup
    --all                  Perform all setup steps
    -h, --help             Show this help message

EXAMPLES:
    $0 --all               Perform complete GPU setup
    $0 --test              Test existing GPU setup

EOF
}

# Parse command line arguments
INSTALL_TOOLKIT=false
INSTALL_DEVICE_PLUGIN=false
LABEL_NODES=false
CREATE_RUNTIME_CLASS=false
TEST_GPU=false
ALL=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --install-toolkit)
            INSTALL_TOOLKIT=true
            shift
            ;;
        --install-device-plugin)
            INSTALL_DEVICE_PLUGIN=true
            shift
            ;;
        --label-nodes)
            LABEL_NODES=true
            shift
            ;;
        --create-runtime-class)
            CREATE_RUNTIME_CLASS=true
            shift
            ;;
        --test)
            TEST_GPU=true
            shift
            ;;
        --all)
            ALL=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# If no specific options, show usage
if [ "$INSTALL_TOOLKIT" = false ] && [ "$INSTALL_DEVICE_PLUGIN" = false ] && [ "$LABEL_NODES" = false ] && [ "$CREATE_RUNTIME_CLASS" = false ] && [ "$TEST_GPU" = false ] && [ "$ALL" = false ]; then
    show_usage
    exit 1
fi

# Main execution
print_status "Starting GPU node setup..."

check_gpu_node

if [ "$ALL" = true ] || [ "$INSTALL_TOOLKIT" = true ]; then
    install_nvidia_container_toolkit
fi

if [ "$ALL" = true ] || [ "$INSTALL_DEVICE_PLUGIN" = true ]; then
    install_nvidia_device_plugin
fi

if [ "$ALL" = true ] || [ "$LABEL_NODES" = true ]; then
    label_gpu_nodes
fi

if [ "$ALL" = true ] || [ "$CREATE_RUNTIME_CLASS" = true ]; then
    create_gpu_runtime_class
fi

if [ "$ALL" = true ] || [ "$TEST_GPU" = true ]; then
    test_gpu_setup
fi

print_success "GPU node setup completed!"
